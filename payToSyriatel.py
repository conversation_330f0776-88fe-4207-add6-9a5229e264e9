import requests
import time
import urllib3
import re
from datetime import datetime, timed<PERSON>ta
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.alert import Alert
from fake_useragent import UserAgent
import random
import os

import sys


# تعطيل تحذيرات SSL الغير مهمة
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)



# License verification removed - running in standalone mode

# API URLs (kept for reference but not used in single order mode)
# url = "https://menfax.com/pos/public/api/orders"
# status_url = "https://menfax.com/pos/public/api/change-order-status"
# secret = "SECRET1265AQREFGHKLFS!@#"

# Logging disabled - no file logging in standalone mode
def log(message, log_type="INFO"):
    """
    Simple console logging only - no file logging
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    full_message = f"[{timestamp}] [{log_type}] {message}"
    print(full_message)

def log_payment_details(order_id, phone_number, amount, status, details=""):
    """
    Payment details logging disabled - console only
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [PAYMENT] Order: {order_id}, Phone: {phone_number}, Amount: {amount}, Status: {status}, Details: {details}")

def random_sleep(min_sec=0.5, max_sec=1.5):
    """
    Sleep for a random duration between min_sec and max_sec seconds
    Args:
        min_sec: Minimum sleep duration
        max_sec: Maximum sleep duration
    """
    time.sleep(random.uniform(min_sec, max_sec))

def determine_payment_type(order):
    """
    Determine payment type based on order data
    Args:
        order: Order data from API
    Returns:
        str: 'bulk' for رصيد جملة, 'cash' for رصيد كاش, None if not supported
    """
    product = order.get("product")
    order_type = order.get("type", "")

    if product == 25:
        if "رصيد وحدات - Syriatel" in order_type:
            return "bulk"
        elif "رصيد كاش - Syriatel" in order_type:
            return "cash"

    return None

def get_payment_config(payment_type):
    """
    Get configuration for specific payment type
    Args:
        payment_type: 'bulk' or 'cash'
    Returns:
        dict: Configuration with URL and max_amount
    """
    configs = {
        "bulk": {
            "url": "https://abili.syriatel.com.sy/Transfer.aspx",
            "max_amount": 1000000,
            "name": "رصيد جملة"
        },
        "cash": {
            "url": "https://abili.syriatel.com.sy/ePaymentTransfer.aspx",
            "max_amount": 10000000,
            "name": "رصيد كاش"
        }
    }
    return configs.get(payment_type)

def process_single_payment(driver, order):
    """
    Process a single payment order for Syriatel
    Args:
        driver: Selenium WebDriver instance
        order: Order data from user input
    Returns:
        bool: True if payment was processed successfully, False otherwise
    """
    order_type = order.get("type", "")
    number = order["phone_number"]
    order_id = str(order.get("id"))
    amount = order["amount"]

    log(f"Processing order ID: {order_id}, Phone: {number}, Amount: {amount}, Type: {order_type}", "INFO")

    # Determine payment type
    payment_type = determine_payment_type(order)
    if not payment_type:
        log(f"Unsupported order type: {order_type}", "ERROR")
        log_payment_details(order_id, number, amount, "2", f"Unsupported order type: {order_type}")
        display_payment_result_syriatel(order_id, 2, "نوع الطلب غير مدعوم")
        return False

    # Get payment configuration
    config = get_payment_config(payment_type)
    log(f"Payment type: {config['name']}, Max amount: {config['max_amount']}", "INFO")

    # Check amount limit
    if int(amount) > config['max_amount']:
        log(f"Payment rejected: Amount {amount} exceeds limit {config['max_amount']}", "WARNING")
        log_payment_details(order_id, number, amount, "2", f"Amount exceeds limit: {amount} > {config['max_amount']}")
        display_payment_result_syriatel(order_id, 2, f"المبلغ ({amount}) يتجاوز الحد الأقصى المسموح ({config['max_amount']})")
        return False

    try:
        # Navigate to payment page
        log(f"Navigating to {config['name']} page", "INFO")
        driver.get(config['url'])
        WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
        random_sleep()

        # Enter customer number/code
        log(f"Entering customer number: {number}", "INFO")
        customer_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "/html/body/form/div[3]/div/div/div[2]/fieldset[2]/p[1]/input[1]"))
        )
        customer_input.clear()
        customer_input.send_keys(number)
        random_sleep()

        # Enter amount
        log(f"Entering amount: {amount}", "INFO")
        amount_input = driver.find_element(By.XPATH, "/html/body/form/div[3]/div/div/div[2]/fieldset[2]/p[3]/input")
        amount_input.clear()
        amount_input.send_keys(amount)
        random_sleep()

        # Click first button to check balance
        log("Clicking check balance button", "INFO")
        check_button = driver.find_element(By.XPATH, "/html/body/form/div[3]/div/div/div[2]/fieldset[2]/p[5]/input")
        check_button.click()
        random_sleep(2)

        # Get balance before payment
        log("Getting balance before payment", "INFO")
        try:
            balance_element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "/html/body/form/div[3]/div/div/div[2]/div[2]/table/tbody/tr/td[3]"))
            )
            balance_before = balance_element.text.strip()
            log(f"Balance before payment: {balance_before}", "INFO")
        except Exception as e:
            log(f"Could not get balance before payment: {e}", "ERROR")
            log_payment_details(order_id, number, amount, "2", f"Could not get balance: {str(e)}")
            display_payment_result_syriatel(order_id, 2, "لا يمكن الحصول على رصيد المشترك")
            return False

        # Click confirm payment button
        log("Clicking confirm payment button", "INFO")
        confirm_button = driver.find_element(By.XPATH, "/html/body/form/div[3]/div/div/div[2]/fieldset[2]/table/tbody/tr[4]/td/p/input[2]")
        confirm_button.click()
        random_sleep()

        # Handle JavaScript alert
        try:
            WebDriverWait(driver, 5).until(EC.alert_is_present())
            alert = Alert(driver)
            alert.accept()
            log("JavaScript alert accepted", "INFO")
            random_sleep(2)
        except:
            log("No JavaScript alert present", "INFO")

        # Check if redirected to error page (for cash payments)
        current_url = driver.current_url
        if "Error.aspx" in current_url:
            log("Agent does not exist - redirected to error page", "ERROR")
            log_payment_details(order_id, number, amount, "2", "Agent does not exist")
            display_payment_result_syriatel(order_id, 2, "Agent does not exist")
            return False

        # Get balance after payment
        log("Getting balance after payment", "INFO")
        try:
            balance_element_after = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "/html/body/form/div[3]/div/div/div[2]/div[2]/table/tbody/tr/td[3]"))
            )
            balance_after = balance_element_after.text.strip()
            log(f"Balance after payment: {balance_after}", "INFO")
        except Exception as e:
            log(f"Could not get balance after payment: {e}", "ERROR")
            balance_after = balance_before

        # Get operation message
        try:
            message_element = driver.find_element(By.XPATH, "/html/body/form/div[3]/div/div/div[2]/fieldset[2]/p[6]/span")
            operation_message = message_element.text.strip()
            log(f"Operation message: {operation_message}", "INFO")
        except:
            operation_message = "لا توجد رسالة"
            log("No operation message found", "WARNING")

        # Check if message contains pattern with three numbers in parentheses
        numbers_in_parentheses = re.findall(r'\(([0-9]+(?:\.[0-9]+)?)\)', operation_message)

        if len(numbers_in_parentheses) >= 3:
            # Take the first number and create simplified message
            first_number = numbers_in_parentheses[0]
            simplified_message = f"المبلغ المطلوب {first_number}"
        else:
            # Keep original message if pattern doesn't match
            simplified_message = operation_message

        # Determine payment success by comparing balances
        try:
            # Remove commas and convert to float safely
            balance_before_clean = balance_before.replace(',', '').replace(' ', '')
            balance_after_clean = balance_after.replace(',', '').replace(' ', '')

            balance_before_num = float(balance_before_clean)
            balance_after_num = float(balance_after_clean)
            expected_balance = balance_before_num - float(amount)

            # Check if balance changed by the expected amount (with small tolerance)
            if abs(balance_after_num - expected_balance) < 1:
                log(f"Payment successful for order {order_id}", "SUCCESS")
                log_payment_details(order_id, number, amount, "1", f"Balance changed from {balance_before} to {balance_after}")
                display_payment_result_syriatel(order_id, 1, simplified_message)
                payment_successful = True
            else:
                log(f"Payment failed for order {order_id} - balance not changed correctly", "ERROR")
                log_payment_details(order_id, number, amount, "2", f"Balance unchanged: {balance_before} -> {balance_after}")
                display_payment_result_syriatel(order_id, 2, simplified_message)
                payment_successful = False

        except Exception as e:
            log(f"Error comparing balances: {e}", "ERROR")
            log_payment_details(order_id, number, amount, "2", f"Balance comparison error: {str(e)}")
            display_payment_result_syriatel(order_id, 2, simplified_message)
            payment_successful = False

        return payment_successful

    except Exception as e:
        log(f"Error processing payment for order {order_id}: {e}", "ERROR")
        log_payment_details(order_id, number, amount, "2", f"Processing error: {str(e)}")
        display_payment_result_syriatel(order_id, 2, f"خطأ في معالجة الطلب: {str(e)}")
        return False

def display_payment_result_syriatel(order_id, status, message=None):
    """
    Display payment result to user (no API sending) - Syriatel version
    Args:
        order_id: Order ID
        status: Status code (1=paid, 2=number not found, 3=payment failed, 4=subscriber has debt)
        message: Optional Arabic message for rejection cases
    """
    status_descriptions = {
        1: "✅ تم الدفع بنجاح",
        2: "❌ الرقم غير موجود في النظام",
        3: "❌ فشل في عملية الدفع",
        4: "❌ المشترك عليه دين"
    }

    print("\n" + "="*50)
    print("📋 نتيجة معالجة الطلب - Syriatel")
    print("="*50)
    print(f"رقم الطلب: {order_id}")
    print(f"حالة الطلب: {status}")
    print(f"الوصف: {status_descriptions.get(status, 'حالة غير معروفة')}")
    if message:
        print(f"رسالة إضافية: {message}")
    print("="*50)

    log(f"Syriatel payment result displayed for order {order_id} with status {status}", "INFO")

def process_single_payment_syriatel(order):
    """
    API wrapper for Syriatel payment processing

    Args:
        order: Order object with id, phone_number, amount, type

    Returns:
        tuple: (success, status, message)
    """
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium_stealth import stealth

        # Initialize browser with enhanced stealth settings
        log("Initializing browser for Syriatel payment", "INFO")
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-images")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")

        # Create driver
        driver = webdriver.Chrome(options=chrome_options)

        # Apply stealth settings
        stealth(driver,
                languages=["en-US", "en"],
                vendor="Google Inc.",
                platform="Win32",
                webgl_vendor="Intel Inc.",
                renderer="Intel Iris OpenGL Engine",
                fix_hairline=True)

        try:
            # Process the payment
            success = process_single_payment(driver, order)

            if success:
                return True, 1, "تم الدفع بنجاح"
            else:
                return False, 3, "فشل في عملية الدفع"

        finally:
            # Clean up browser
            try:
                driver.quit()
                log("Browser closed successfully", "INFO")
            except:
                pass

    except Exception as e:
        log(f"Error in Syriatel payment processing: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_single_payment_syriatel_api(order):
    """
    API wrapper for Syriatel payment processing that returns detailed status information

    Args:
        order: Order object with id, phone_number, amount, type

    Returns:
        tuple: (success, status, message)
    """
    order_type = order.get("type", "")
    number = order["phone_number"]
    order_id = str(order.get("id"))
    amount = order["amount"]

    log(f"Processing Syriatel order ID: {order_id}, Phone: {number}, Amount: {amount}, Type: {order_type}", "INFO")

    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium_stealth import stealth

        # Chrome options for headless operation
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")

        # Create driver
        driver = webdriver.Chrome(options=chrome_options)

        # Apply stealth settings
        stealth(driver,
                languages=["en-US", "en"],
                vendor="Google Inc.",
                platform="Win32",
                webgl_vendor="Intel Inc.",
                renderer="Intel Iris OpenGL Engine",
                fix_hairline=True)

        try:
            # Navigate to Syriatel payment page
            log("Navigating to Syriatel payment page", "INFO")
            driver.get("https://www.syriatel.sy/ar/recharge")
            WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
            random_sleep()

            # Enter phone number
            log(f"Entering phone number: {number}", "INFO")
            phone_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "phone"))
            )
            phone_input.clear()
            phone_input.send_keys(number)
            random_sleep()

            # Enter amount
            log(f"Entering amount: {amount}", "INFO")
            amount_input = driver.find_element(By.ID, "amount")
            amount_input.clear()
            amount_input.send_keys(str(amount))
            random_sleep()

            # Get balance before payment
            log("Getting balance before payment", "INFO")
            try:
                balance_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "balance-display"))
                )
                balance_before = balance_element.text.strip()
                log(f"Balance before payment: {balance_before}", "INFO")
            except:
                balance_before = "0"
                log("Could not get balance before payment", "WARNING")

            # Submit payment
            log("Submitting payment", "INFO")
            submit_button = driver.find_element(By.ID, "submit-payment")
            submit_button.click()
            random_sleep()

            # Wait for payment processing
            log("Waiting for payment processing", "INFO")
            time.sleep(5)

            # Check for error messages
            try:
                error_element = driver.find_element(By.CLASS_NAME, "error-message")
                error_text = error_element.text.strip()

                if error_text:
                    log(f"Payment error detected: {error_text}", "ERROR")

                    # Check for specific error types
                    if "غير موجود" in error_text or "not found" in error_text.lower():
                        log_payment_details(order_id, number, amount, "2", f"Number not found: {error_text}")
                        return False, 2, "الرقم غير موجود في النظام"
                    elif "دين" in error_text or "debt" in error_text.lower():
                        log_payment_details(order_id, number, amount, "4", f"Subscriber has debt: {error_text}")
                        return False, 4, error_text
                    else:
                        log_payment_details(order_id, number, amount, "3", f"Payment error: {error_text}")
                        return False, 3, "فشل في عملية الدفع"

            except:
                # No error message found, continue
                pass

            # Get balance after payment
            log("Getting balance after payment", "INFO")
            try:
                balance_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "balance-display"))
                )
                balance_after = balance_element.text.strip()
                log(f"Balance after payment: {balance_after}", "INFO")
            except:
                balance_after = "0"
                log("Could not get balance after payment", "WARNING")

            # Determine payment success based on balance change
            try:
                balance_before_num = float(balance_before.replace(',', '').replace(' ', ''))
                balance_after_num = float(balance_after.replace(',', '').replace(' ', ''))
                expected_balance = balance_before_num - float(amount)

                # Check if balance changed by the expected amount (with small tolerance)
                if abs(balance_after_num - expected_balance) < 1:
                    log(f"Syriatel payment successful for order {order_id}", "SUCCESS")
                    log_payment_details(order_id, number, amount, "1", f"Balance changed from {balance_before} to {balance_after}")
                    return True, 1, "تم الدفع بنجاح"
                else:
                    log(f"Syriatel payment failed for order {order_id} - balance not changed correctly", "ERROR")
                    log_payment_details(order_id, number, amount, "3", f"Balance unchanged: {balance_before} -> {balance_after}")
                    return False, 3, "فشل في عملية الدفع"

            except Exception as e:
                log(f"Error comparing balances: {e}", "ERROR")
                log_payment_details(order_id, number, amount, "3", f"Balance comparison error: {str(e)}")
                return False, 3, "فشل في عملية الدفع"

        finally:
            # Clean up browser
            try:
                driver.quit()
                log("Browser closed successfully", "INFO")
            except:
                pass

    except Exception as e:
        log(f"Error in Syriatel payment processing: {e}", "ERROR")
        log_payment_details(order_id, number, amount, "3", f"Processing error: {str(e)}")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_single_order_from_input_syriatel():
    """
    Process a single Syriatel order from user input instead of fetching from API
    """
    try:
        # Initialize browser with enhanced stealth settings
        log("Initializing browser with stealth settings", "INFO")
        ua = UserAgent()
        user_agent = ua.random

        options = Options()
        options.add_argument(f"user-agent={user_agent}")
        options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--disable-infobars")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-gpu")
        options.add_argument("--window-size=1366,768")
        options.add_argument("--disable-web-security")
        options.add_argument("--allow-running-insecure-content")
        options.add_argument("--disable-logging")
        options.add_argument("--log-level=3")
        options.add_argument("--silent")
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-plugins")
        options.add_argument("--disable-background-networking")
        options.add_argument("--disable-sync")
        options.add_argument("--disable-translate")
        options.add_argument("--disable-features=VizDisplayCompositor,TranslateUI")
        options.add_argument("--no-first-run")
        options.add_argument("--no-default-browser-check")

        # إعدادات إضافية لإخفاء الرسائل
        options.add_experimental_option("prefs", {
            "profile.default_content_setting_values.notifications": 2
        })

        # Use webdriver-manager to automatically download and manage ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)

        # Enhanced anti-detection measures
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'ar']});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
                Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 4});
                window.chrome = {runtime: {}};
            """
        })

        # Login to Syriatel system
        log("Starting login process to Syriatel", "INFO")
        driver.get("https://abili.syriatel.com.sy/Login.aspx")
        WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
        random_sleep()

        # Enter credentials
        log("Entering username", "INFO")
        username_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "UsernameTextBox"))
        )
        username_field.clear()
        username_field.send_keys("WSLRETCOS310")
        random_sleep()

        log("Entering password", "INFO")
        password_field = driver.find_element(By.ID, "PasswordTextBox")
        password_field.clear()
        password_field.send_keys("Ea@456NN")
        random_sleep()

        log("Clicking login button", "INFO")
        submit_button = driver.find_element(By.ID, "SubmitButton")
        submit_button.click()

        # Wait for login to complete
        time.sleep(5)
        current_url = driver.current_url
        if "Login.aspx" not in current_url:
            log("Login successful to Syriatel system", "SUCCESS")
        else:
            log("Login failed to Syriatel system", "ERROR")
            return

        print("=== معالج دفع Syriatel - طلب واحد ===")
        print("يرجى إدخال بيانات الطلب:")

        # Get order details from user input
        order_id = input("رقم الطلب (Order ID): ").strip()
        phone_number = input("رقم الهاتف: ").strip()
        amount = input("المبلغ: ").strip()

        if not order_id or not phone_number or not amount:
            print("❌ يجب إدخال جميع البيانات المطلوبة")
            return False

        try:
            amount = float(amount)
        except ValueError:
            print("❌ المبلغ يجب أن يكون رقم صحيح")
            return False

        # Create order object similar to API format
        order = {
            "id": order_id,
            "phone_number": phone_number,
            "amount": amount,
            "product": 25  # Syriatel product ID
        }

        print(f"\n📋 معلومات الطلب:")
        print(f"رقم الطلب: {order_id}")
        print(f"رقم الهاتف: {phone_number}")
        print(f"المبلغ: {amount}")
        print("\n🔄 بدء معالجة الطلب...")

        # Process the single order
        success = process_single_payment(driver, order)

        if success:
            print(f"\n✅ تم معالجة الطلب {order_id} بنجاح")
            log(f"Order {order_id} processed successfully", "SUCCESS")
            return True
        else:
            print(f"\n❌ فشل في معالجة الطلب {order_id}")
            log(f"Order {order_id} processing failed", "ERROR")
            return False

    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف العملية بواسطة المستخدم")
        return False

    except Exception as e:
        log(f"Critical error in main execution: {e}", "ERROR")
    finally:
        try:
            if 'driver' in locals() and driver is not None:
                driver.quit()
                log("Browser closed successfully", "INFO")
        except Exception as e:
            log(f"Error closing browser: {str(e)}", "ERROR")

if __name__ == "__main__":
    process_single_order_from_input_syriatel()
