#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Order Rejection Script
Simple script to reject orders by sending status 3 to API
"""

import requests
import sys
from datetime import datetime

# API Configuration
status_url = "https://menfax.com/pos/public/api/change-order-status"
secret = "SECRET1265AQREFGHKLFS!@#"

def reject_order(order_id, reason="تم رفض الطلب يدوياً"):
    """
    Send rejection status (3) to API for given order ID
    Args:
        order_id: Order ID to reject
        reason: Arabic reason for rejection
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Prepare payload
        payload = {
            "id": int(order_id),
            "status": 1,  # Status 3 = PAYMENT FAILED
            "secret": secret,
            "message": reason
        }
        
        # Headers
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Origin": "https://menfax.com",
            "Referer": "https://menfax.com/",
            "Connection": "keep-alive"
        }
        
        print(f"🔄 Sending rejection for order {order_id}...")
        print(f"📝 Reason: {reason}")
        
        # Send request
        response = requests.post(status_url, json=payload, headers=headers, timeout=10, verify=False)
        
        print(f"📡 Response Status: {response.status_code}")
        print(f"📄 Response Body: {response.text}")
        
        if response.status_code == 200:
            print(f"✅ Order {order_id} rejected successfully!")
            return True
        else:
            print(f"❌ Failed to reject order {order_id}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function"""
    print("🚫 QUICK ORDER REJECTION TOOL")
    print("="*40)
    
    try:
        # Get order ID from user
        order_id = input("📋 Enter Order ID to reject: ").strip()
        
        if not order_id:
            print("❌ Order ID cannot be empty!")
            return
        
        if not order_id.isdigit():
            print("❌ Order ID must be a number!")
            return
        
        # Get optional reason
        reason = input("📝 Enter rejection reason (optional, press Enter for default): ").strip()
        if not reason:
            reason = "تم رفض الطلب يدوياً"
        
        # Confirm action
        print(f"\n⚠️  CONFIRMATION:")
        print(f"📋 Order ID: {order_id}")
        print(f"📝 Reason: {reason}")
        print(f"🕐 Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        confirm = input("\n❓ Are you sure you want to reject this order? (y/N): ").strip().lower()
        
        if confirm in ['y', 'yes']:
            success = reject_order(order_id, reason)
            if success:
                print(f"\n🎉 Order {order_id} has been rejected successfully!")
            else:
                print(f"\n💥 Failed to reject order {order_id}")
        else:
            print("🚫 Operation cancelled by user")
            
    except KeyboardInterrupt:
        print("\n\n👋 Operation cancelled by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
