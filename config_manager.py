#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration Manager for Payment Companies
==========================================

This module handles loading and managing configuration for all payment companies
from the companies_config.json file.
"""

import json
import os
from datetime import datetime

class ConfigManager:
    """Manages configuration for all payment companies"""
    
    def __init__(self, config_file="companies_config.json"):
        """
        Initialize configuration manager
        
        Args:
            config_file: Path to the JSON configuration file
        """
        self.config_file = config_file
        self.config = None
        self.load_config()
    
    def load_config(self):
        """Load configuration from JSON file"""
        try:
            if not os.path.exists(self.config_file):
                raise FileNotFoundError(f"Configuration file {self.config_file} not found")
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            print(f"✅ Configuration loaded successfully from {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error loading configuration: {e}")
            return False
    
    def save_config(self):
        """Save current configuration to JSON file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Configuration saved successfully to {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving configuration: {e}")
            return False
    
    def get_company_config(self, company_key):
        """
        Get configuration for a specific company
        
        Args:
            company_key: Company key (e.g., 'sawa', 'syriatel', 'bitakat')
            
        Returns:
            dict: Company configuration or None if not found
        """
        if not self.config:
            return None
        
        return self.config.get('companies', {}).get(company_key)
    
    def get_company_by_product_id(self, product_id):
        """
        Get company configuration by product ID
        
        Args:
            product_id: Product ID (e.g., 29, 25, 50)
            
        Returns:
            tuple: (company_key, company_config) or (None, None) if not found
        """
        if not self.config:
            return None, None
        
        for company_key, company_config in self.config.get('companies', {}).items():
            if company_config.get('product_id') == product_id:
                return company_key, company_config
        
        return None, None
    
    def get_login_credentials(self, company_key):
        """
        Get login credentials for a company
        
        Args:
            company_key: Company key
            
        Returns:
            dict: Login credentials (url, username, password, method, etc.)
        """
        company_config = self.get_company_config(company_key)
        if company_config:
            return company_config.get('login', {})
        return {}
    
    def get_payment_limits(self, company_key):
        """
        Get payment limits for a company
        
        Args:
            company_key: Company key
            
        Returns:
            dict: Payment limits (max_amount, min_amount, etc.)
        """
        company_config = self.get_company_config(company_key)
        if company_config:
            return company_config.get('payment', {})
        return {}
    
    def get_debt_validation_rules(self, company_key):
        """
        Get debt validation rules for a company
        
        Args:
            company_key: Company key
            
        Returns:
            dict: Debt validation rules
        """
        company_config = self.get_company_config(company_key)
        if company_config:
            return company_config.get('validation', {}).get('debt_rules', {})
        return {}
    
    def get_global_settings(self):
        """
        Get global settings
        
        Returns:
            dict: Global settings
        """
        if not self.config:
            return {}
        
        return self.config.get('global_settings', {})
    
    def get_status_descriptions(self):
        """
        Get status code descriptions
        
        Returns:
            dict: Status code descriptions
        """
        global_settings = self.get_global_settings()
        return global_settings.get('status_codes', {})
    
    def update_company_credentials(self, company_key, username=None, password=None):
        """
        Update login credentials for a company
        
        Args:
            company_key: Company key
            username: New username (optional)
            password: New password (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            company_config = self.get_company_config(company_key)
            if not company_config:
                print(f"❌ Company {company_key} not found")
                return False
            
            if username:
                company_config['login']['username'] = username
                print(f"✅ Updated username for {company_key}")
            
            if password:
                company_config['login']['password'] = password
                print(f"✅ Updated password for {company_key}")
            
            return self.save_config()
            
        except Exception as e:
            print(f"❌ Error updating credentials for {company_key}: {e}")
            return False
    
    def update_payment_limits(self, company_key, max_amount=None, min_amount=None):
        """
        Update payment limits for a company
        
        Args:
            company_key: Company key
            max_amount: New maximum amount (optional)
            min_amount: New minimum amount (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            company_config = self.get_company_config(company_key)
            if not company_config:
                print(f"❌ Company {company_key} not found")
                return False
            
            if max_amount:
                company_config['payment']['max_amount'] = max_amount
                print(f"✅ Updated max_amount for {company_key} to {max_amount}")
            
            if min_amount:
                company_config['payment']['min_amount'] = min_amount
                print(f"✅ Updated min_amount for {company_key} to {min_amount}")
            
            return self.save_config()
            
        except Exception as e:
            print(f"❌ Error updating payment limits for {company_key}: {e}")
            return False
    
    def list_all_companies(self):
        """
        List all configured companies
        
        Returns:
            list: List of company information
        """
        if not self.config:
            return []
        
        companies = []
        for company_key, company_config in self.config.get('companies', {}).items():
            # Handle special case for Syriatel which has bulk/cash structure
            payment_config = company_config.get('payment', {})
            max_amount = payment_config.get('max_amount')

            # For Syriatel, get max amount from cash config
            if not max_amount and 'cash' in payment_config:
                max_amount = payment_config['cash'].get('max_amount')

            companies.append({
                'key': company_key,
                'product_id': company_config.get('product_id'),
                'name': company_config.get('name'),
                'name_en': company_config.get('name_en'),
                'username': company_config.get('login', {}).get('username'),
                'max_amount': max_amount
            })
        
        return companies

# Global instance
config_manager = ConfigManager()

def get_config():
    """Get the global configuration manager instance"""
    return config_manager

def reload_config():
    """Reload configuration from file"""
    return config_manager.load_config()

# Convenience functions
def get_company_credentials(company_key):
    """Get login credentials for a company"""
    return config_manager.get_login_credentials(company_key)

def get_company_limits(company_key):
    """Get payment limits for a company"""
    return config_manager.get_payment_limits(company_key)

def get_company_by_product(product_id):
    """Get company configuration by product ID"""
    return config_manager.get_company_by_product_id(product_id)

if __name__ == "__main__":
    # Test the configuration manager
    print("🧪 Testing Configuration Manager")
    print("=" * 40)
    
    # List all companies
    companies = config_manager.list_all_companies()
    print(f"\n📋 Found {len(companies)} companies:")
    for company in companies:
        print(f"  • {company['name']} ({company['name_en']}) - Product ID: {company['product_id']}")
        print(f"    Username: {company['username']}, Max Amount: {company['max_amount']}")
    
    # Test getting specific company config
    print(f"\n🔍 Testing Sawa configuration:")
    sawa_config = config_manager.get_company_config('sawa')
    if sawa_config:
        print(f"  Username: {sawa_config['login']['username']}")
        print(f"  Max Amount: {sawa_config['payment']['max_amount']}")
    
    print("\n✅ Configuration manager test completed!")
