#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Payment Request Sender
======================

Script to send payment requests to the Payment Processing API.
"""

import requests
import json
from datetime import datetime, timezone

# API Configuration
API_BASE_URL = "http://localhost:5000"
PAYMENT_ENDPOINT = "/process-payment"

def send_payment_request(order_data):
    """
    Send payment request to the API
    
    Args:
        order_data (dict): Order data to send
        
    Returns:
        dict: API response or error information
    """
    try:
        url = f"{API_BASE_URL}{PAYMENT_ENDPOINT}"
        
        print(f"🔄 Sending payment request to: {url}")
        print(f"📋 Request data:")
        print(json.dumps(order_data, indent=2, ensure_ascii=False))
        print("\n" + "="*50)
        
        # Send POST request
        response = requests.post(
            url,
            json=order_data,
            headers={
                "Content-Type": "application/json",
                "Accept": "application/json"
            },
            timeout=300  # 5 minutes timeout
        )
        
        print(f"📡 Response Status: {response.status_code}")
        
        # Parse response
        if response.headers.get('content-type', '').startswith('application/json'):
            result = response.json()
            print(f"📄 Response Body:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            result = {
                "error": "Non-JSON response",
                "status_code": response.status_code,
                "content": response.text
            }
            print(f"❌ Non-JSON response: {response.text}")
        
        return {
            "success": response.status_code == 200,
            "status_code": response.status_code,
            "data": result
        }
        
    except requests.exceptions.ConnectionError:
        error_msg = "❌ Connection failed. Make sure the API server is running on http://localhost:5000"
        print(error_msg)
        return {
            "success": False,
            "error": "Connection failed",
            "message": error_msg
        }
    except requests.exceptions.Timeout:
        error_msg = "⏰ Request timed out (5 minutes)"
        print(error_msg)
        return {
            "success": False,
            "error": "Timeout",
            "message": error_msg
        }
    except Exception as e:
        error_msg = f"❌ Error: {str(e)}"
        print(error_msg)
        return {
            "success": False,
            "error": "Exception",
            "message": error_msg
        }

def create_sample_request():
    """Create the sample request as specified"""
    return {
        "id": 532523,
        "amount": "24000",
        "number": "08632565",
        "code": None,
        "product": 50,
        "type": "2 MB",
        "date": "2025-08-07T08:22:28.000000Z"
    }

def create_custom_request():
    """Create a custom request from user input"""
    print("📝 إنشاء طلب دفع مخصص:")
    print("="*40)
    
    try:
        order_id = input("رقم الطلب (Order ID): ").strip()
        amount = input("المبلغ (Amount): ").strip()
        number = input("رقم الهاتف (Phone Number): ").strip()
        product = input("معرف المنتج (Product ID): ").strip()
        order_type = input("نوع الطلب (Type) [اختياري]: ").strip()
        
        if not order_id or not amount or not number or not product:
            print("❌ يجب إدخال جميع البيانات المطلوبة")
            return None
        
        # Validate inputs
        try:
            order_id = int(order_id)
            product = int(product)
            float(amount)  # Validate amount is numeric
        except ValueError:
            print("❌ رقم الطلب ومعرف المنتج يجب أن يكونا أرقام صحيحة، والمبلغ يجب أن يكون رقم")
            return None
        
        return {
            "id": order_id,
            "amount": amount,
            "number": number,
            "code": None,
            "product": product,
            "type": order_type if order_type else "Custom Order",
            "date": datetime.now(timezone.utc).isoformat()
        }
        
    except KeyboardInterrupt:
        print("\n❌ تم إلغاء العملية")
        return None

def show_supported_products():
    """Show supported product IDs"""
    print("\n🏢 معرفات المنتجات المدعومة:")
    print("="*40)
    products = {
        25: "سيرياتيل (Syriatel)",
        29: "سوا (Sawa)",
        46: "لاينت (Linet)",
        47: "إنت (INET)",
        48: "إم تي إس (MTS)",
        49: "ليما (LEMA)",
        50: "بطاقات (Bitakat)",
        51: "تكامل (Takamol)"
    }
    
    for product_id, name in products.items():
        print(f"   {product_id}: {name}")
    print()

def main():
    """Main function"""
    print("🚀 مرسل طلبات الدفع")
    print("="*50)
    
    while True:
        print("\nاختر نوع الطلب:")
        print("1. إرسال الطلب النموذجي (المحدد مسبقاً)")
        print("2. إنشاء طلب مخصص")
        print("3. عرض معرفات المنتجات المدعومة")
        print("4. خروج")
        
        choice = input("\nاختيارك (1-4): ").strip()
        
        if choice == "1":
            print("\n📤 إرسال الطلب النموذجي...")
            order_data = create_sample_request()
            result = send_payment_request(order_data)
            
            if result["success"]:
                print("\n✅ تم إرسال الطلب بنجاح!")
            else:
                print(f"\n❌ فشل في إرسال الطلب: {result.get('message', 'Unknown error')}")
        
        elif choice == "2":
            print("\n📝 إنشاء طلب مخصص...")
            show_supported_products()
            order_data = create_custom_request()
            
            if order_data:
                result = send_payment_request(order_data)
                
                if result["success"]:
                    print("\n✅ تم إرسال الطلب بنجاح!")
                else:
                    print(f"\n❌ فشل في إرسال الطلب: {result.get('message', 'Unknown error')}")
            else:
                print("❌ لم يتم إنشاء الطلب")
        
        elif choice == "3":
            show_supported_products()
        
        elif choice == "4":
            print("👋 شكراً لاستخدام مرسل طلبات الدفع!")
            break
        
        else:
            print("❌ اختيار غير صحيح. يرجى اختيار رقم من 1 إلى 4.")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
