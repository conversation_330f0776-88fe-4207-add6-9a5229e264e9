{"companies": {"sawa": {"product_id": 29, "name": "سوا", "name_en": "<PERSON><PERSON>", "login": {"url": "http://sp.sawaisp.sy/charge_balance_to_customer", "username": "fireware", "password": "idhmhgauvhx", "method": "selenium"}, "payment": {"max_amount": 100000, "min_amount": 1, "debt_tolerance": 140, "phone_prefix": "", "duplicate_check_hours": 24}, "urls": {"payment_page": "http://sp.sawaisp.sy/charge_balance_to_customer"}, "validation": {"debt_rules": {"tolerance": 140, "allow_overpayment": true, "zero_debt_exception": true}}}, "syriatel": {"product_id": 25, "name": "سيرياتيل", "name_en": "Syriatel", "login": {"url": "https://abili.syriatel.com.sy/Login.aspx", "username": "WSLRETCOS310", "password": "Ea@456NN", "method": "selenium"}, "payment": {"bulk": {"url": "https://abili.syriatel.com.sy/Transfer.aspx", "max_amount": 1000000, "name": "رصي<PERSON> ج<PERSON>ة"}, "cash": {"url": "https://abili.syriatel.com.sy/ePaymentTransfer.aspx", "max_amount": 10000000, "name": "ر<PERSON>ي<PERSON> كاش"}, "min_amount": 1, "phone_prefix": "", "duplicate_check_hours": 0}, "validation": {"debt_rules": {"tolerance": 0, "allow_overpayment": true, "zero_debt_exception": false}}}, "bitakat": {"product_id": 50, "name": "بطاقات", "name_en": "<PERSON><PERSON><PERSON>", "login": {"url": "https://pos.ispcare.bitakat.sy/", "username": "naoras-tech", "password": "Idhm*9hgauvhx", "method": "requests", "login_data": {"action": "login", "login": "إرسال"}}, "payment": {"max_amount": 100000, "min_amount": 1, "debt_tolerance": 140, "phone_prefix": "41", "duplicate_check_minutes": 5}, "urls": {"base_url": "https://pos.ispcare.bitakat.sy/", "search_url": "https://pos.ispcare.bitakat.sy/?&pg={pg}&papp={papp}", "payment_form_url": "https://pos.ispcare.bitakat.sy/?&pg={pg}&papp={papp}&pa=add_payment&sub_id={sub_id}", "confirmation_url": "https://pos.ispcare.bitakat.sy/?&pg={pg}&papp={papp}"}, "validation": {"debt_rules": {"tolerance": 140, "allow_overpayment": true, "zero_debt_exception": true}}}, "linet": {"product_id": 46, "name": "لاينت", "name_en": "Linet", "login": {"url": "https://pos.ispcare.linet-sy.com/", "username": "nawras-tel", "password": "Nn@456nn", "method": "requests", "login_data": {"action": "login", "lang": "ar", "login": "إرسال"}}, "payment": {"max_amount": 100000, "min_amount": 1, "debt_tolerance": 140, "phone_prefix": "41", "duplicate_check_minutes": 5}, "urls": {"base_url": "https://pos.ispcare.linet-sy.com/", "search_url": "https://pos.ispcare.linet-sy.com/?&pg={pg}&papp={papp}", "payment_form_url": "https://pos.ispcare.linet-sy.com/?&pg={pg}&papp={papp}&pa=add_payment&sub_id={sub_id}", "confirmation_url": "https://pos.ispcare.linet-sy.com/?&pg={pg}&papp={papp}"}, "validation": {"debt_rules": {"tolerance": 140, "allow_overpayment": true, "zero_debt_exception": true}}}, "lema": {"product_id": 49, "name": "ليما", "name_en": "LEMA", "login": {"url": "https://pos.ispcare.lema.sy/", "username": "ahmad-s", "password": "Meme.meme07", "method": "requests", "login_data": {"action": "login", "login": "إرسال"}}, "payment": {"max_amount": 100000, "min_amount": 1, "debt_tolerance": 140, "phone_prefix": "41", "duplicate_check_minutes": 5}, "urls": {"base_url": "https://pos.ispcare.lema.sy/", "search_url": "https://pos.ispcare.lema.sy/?&pg={pg}&papp={papp}", "payment_form_url": "https://pos.ispcare.lema.sy/?&pg={pg}&papp={papp}&pa=add_payment&sub_id={sub_id}", "confirmation_url": "https://pos.ispcare.lema.sy/?&pg={pg}&papp={papp}"}, "validation": {"debt_rules": {"tolerance": 140, "allow_overpayment": true, "zero_debt_exception": true}}}, "mts": {"product_id": 48, "name": "<PERSON><PERSON> تي إس", "name_en": "MTS", "login": {"url": "https://pos.ispcare.mts.sy/", "username": "alfager.net", "password": "Fager@123", "method": "requests", "login_data": {"action": "login", "login": "إرسال"}}, "payment": {"max_amount": 100000, "min_amount": 1, "debt_tolerance": 140, "phone_prefix": "41", "duplicate_check_minutes": 5}, "urls": {"base_url": "https://pos.ispcare.mts.sy/", "search_url": "https://pos.ispcare.mts.sy/?&pg={pg}&papp={papp}", "payment_form_url": "https://pos.ispcare.mts.sy/?&pg={pg}&papp={papp}&pa=add_payment&sub_id={sub_id}", "confirmation_url": "https://pos.ispcare.mts.sy/?&pg={pg}&papp={papp}"}, "validation": {"debt_rules": {"tolerance": 140, "allow_overpayment": true, "zero_debt_exception": true}}}, "inet": {"product_id": 47, "name": "إنت", "name_en": "INET", "login": {"url": "https://pos.ispcare.inet.sy/", "username": "naw<PERSON><PERSON><PERSON>", "password": "Idhm*0hgauvhx", "method": "requests", "login_data": {"action": "login", "login": "إرسال"}}, "payment": {"max_amount": 100000, "min_amount": 1, "debt_tolerance": 140, "phone_prefix": "41", "duplicate_check_minutes": 5}, "urls": {"base_url": "https://pos.ispcare.inet.sy/", "search_url": "https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}", "payment_form_url": "https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}&pa=add_payment&sub_id={sub_id}", "confirmation_url": "https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}"}, "validation": {"debt_rules": {"tolerance": 140, "allow_overpayment": true, "zero_debt_exception": true}}}, "takamol": {"product_id": 51, "name": "تكامل", "name_en": "Takamol", "login": {"url": "https://pos.ispcare.takamol.sy/", "username": "firewire", "password": "Idhm*0hgauvhx", "method": "requests", "login_data": {"action": "login", "lang": "ar", "login": "إرسال"}}, "payment": {"max_amount": 100000, "min_amount": 1, "debt_tolerance": 140, "phone_prefix": "41", "duplicate_check_minutes": 5}, "urls": {"base_url": "https://pos.ispcare.takamol.sy/", "search_url": "https://pos.ispcare.takamol.sy/?&pg={pg}&papp={papp}", "payment_form_url": "https://pos.ispcare.takamol.sy/?&pg={pg}&papp={papp}&pa=add_payment&sub_id={sub_id}", "confirmation_url": "https://pos.ispcare.takamol.sy/?&pg={pg}&papp={papp}"}, "validation": {"debt_rules": {"tolerance": 140, "allow_overpayment": true, "zero_debt_exception": true}}}}, "global_settings": {"api": {"base_url": "https://menfax.com/pos/public/api", "orders_endpoint": "/orders", "status_endpoint": "/change-order-status", "secret": "SECRET1265AQREFGHKLFS!@#"}, "status_codes": {"1": "تم الدفع بنجاح", "2": "الرقم غير موجود في النظام", "3": "فشل في عملية الدفع", "4": "المشترك عليه دين"}, "default_timeouts": {"login_timeout": 15, "payment_timeout": 30, "search_timeout": 10}, "selenium_settings": {"headless": true, "window_size": "1920,1080", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}}