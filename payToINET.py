#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Automated inet Payment Processor - Based on Sawa System
Processes inet payment orders automatically without user confirmation
"""

import requests
import time
import urllib3
from datetime import datetime, timedelta
import random
import os

import sys

# Try to import BeautifulSoup, install if not available
try:
    from bs4 import BeautifulSoup
except ImportError:
    print("Installing BeautifulSoup4...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "beautifulsoup4"])
    from bs4 import BeautifulSoup

# Try to import fake_useragent, install if not available
try:
    from fake_useragent import UserAgent
except ImportError:
    print("Installing fake-useragent...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fake-useragent"])
    from fake_useragent import UserAgent

# License verification removed - running in standalone mode

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# API Configuration (kept for reference but not used in single order mode)
# url = "https://menfax.com/pos/public/api/orders"
# status_url = "https://menfax.com/pos/public/api/change-order-status"
# secret = "SECRET1265AQREFGHKLFS!@#"

# Logging disabled - no file logging in standalone mode
def log(message, log_type="INFO"):
    """
    Simple console logging only - no file logging
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    full_message = f"[{timestamp}] [{log_type}] {message}"
    print(full_message)

def log_payment_details(order_id, phone_number, amount, status, details=""):
    """
    Payment details logging disabled - console only
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [PAYMENT] Order: {order_id}, Phone: {phone_number}, Amount: {amount}, Status: {status}, Details: {details}")

def check_duplicate_payment_inet(phone_number, amount):
    """
    Check if the same amount was paid for the same number within 5 minutes (inet specific)
    Args:
        phone_number: Customer phone number
        amount: Payment amount
    Returns:
        bool: True if duplicate found, False otherwise
    """
    if not os.path.exists("inet_payment_tracking.txt"):
        log("No payment tracking file found - no duplicates possible", "INFO")
        return False

    current_time = datetime.now()
    five_minutes_ago = current_time - timedelta(minutes=5)

    try:
        with open("inet_payment_tracking.txt", "r", encoding="utf-8") as f:
            for line in f:
                parts = line.strip().split("|")
                if len(parts) >= 5:
                    payment_time = datetime.strptime(parts[0], "%Y-%m-%d %H:%M:%S")
                    payment_phone = parts[2]
                    payment_amount = parts[3]
                    payment_status = parts[4]

                    # Check if payment was successful and within 5 minutes
                    if (payment_time >= five_minutes_ago and
                        payment_phone == phone_number and
                        payment_amount == amount and
                        payment_status == "1"):
                        log(f"Duplicate payment found: {phone_number} - {amount} at {parts[0]}", "WARNING")
                        return True

        log(f"No duplicate payment found for {phone_number} - {amount} within 5 minutes", "INFO")
    except Exception as e:
        log(f"Error checking duplicate payments: {e}", "ERROR")

    return False

def random_sleep(min_sec=0.5, max_sec=1.5):
    """
    Sleep for a random duration between min_sec and max_sec seconds
    Args:
        min_sec: Minimum sleep duration
        max_sec: Maximum sleep duration
    """
    time.sleep(random.uniform(min_sec, max_sec))

def extract_subscriber_info_inet(html_content):
    """
    Extract subscriber information from inet search results HTML table
    Args:
        html_content: HTML content containing the search results table
    Returns:
        dict: Subscriber information or None if extraction fails
    """
    try:
        import re

        soup = BeautifulSoup(html_content, 'html.parser')

        # Find the table row with subscriber data (class="dark")
        subscriber_row = soup.find('tr', class_='dark')
        if not subscriber_row:
            log("No subscriber data row found in HTML", "ERROR")
            return None

        # Extract all table cells
        cells = subscriber_row.find_all('td')
        if len(cells) < 8:
            log(f"Insufficient table cells found: {len(cells)}, expected at least 8", "ERROR")
            return None

        # Extract information according to the structure you provided:
        # 1st cell: Name (we'll extract the phone number from href if needed)
        # 2nd cell: Phone number
        # 3rd cell: Service type
        # 4th cell: Search success status
        # 5th cell: Subscription type
        # 6th cell: Monthly cost (package price)
        # 7th cell: Subscription status (not needed)
        # 8th cell: Balance (check color for debt status)

        subscriber_info = {
            'name': cells[0].get_text(strip=True),
            'phone_number': cells[1].get_text(strip=True),
            'service_type': cells[2].get_text(strip=True),
            'search_status': cells[3].get_text(strip=True),
            'subscription_type': cells[4].get_text(strip=True),
            'monthly_cost': cells[5].get_text(strip=True),
            'subscription_status': cells[6].get_text(strip=True),
            'balance_text': cells[7].get_text(strip=True),
            'balance_color': None,
            'has_debt': False,
            'debt_amount': 0.0
        }

        # Extract balance color from style attribute
        balance_cell = cells[7]
        style_attr = balance_cell.get('style', '')
        if 'color:' in style_attr:
            # Extract color value
            color_match = re.search(r'color:\s*([^;]+)', style_attr)
            if color_match:
                subscriber_info['balance_color'] = color_match.group(1).strip()

        # Determine debt status based on color
        # Green color means no debt, other colors (or no color) may indicate debt
        balance_color = subscriber_info['balance_color']
        if balance_color and balance_color.lower() in ['green', '#008000', '#00ff00']:
            subscriber_info['has_debt'] = False
            log(f"Balance color is green ({balance_color}) - no debt detected", "INFO")
        else:
            # If color is not green, check if balance is negative or parse for debt
            balance_text = subscriber_info['balance_text']
            try:
                balance_value = float(balance_text.replace(',', ''))
                if balance_value < 0:
                    subscriber_info['has_debt'] = True
                    subscriber_info['debt_amount'] = abs(balance_value)
                    log(f"Negative balance detected: {balance_value} - debt amount: {subscriber_info['debt_amount']}", "WARNING")
                else:
                    subscriber_info['has_debt'] = False
                    log(f"Positive balance but non-green color ({balance_color}) - no debt", "INFO")
            except ValueError:
                log(f"Could not parse balance value: {balance_text}", "WARNING")
                subscriber_info['has_debt'] = False

        log(f"Extracted subscriber info: {subscriber_info['name']} - {subscriber_info['phone_number']}", "INFO")
        log(f"Balance: {subscriber_info['balance_text']} (Color: {subscriber_info['balance_color']}) - Debt: {subscriber_info['has_debt']}", "INFO")

        return subscriber_info

    except Exception as e:
        log(f"Error extracting subscriber info from HTML: {e}", "ERROR")
        return None

def validate_debt_payment_inet(debt_amount, payment_amount):
    """
    Validate payment against debt amount for inet (similar to Sawa rules)
    Args:
        debt_amount: Subscriber's debt amount
        payment_amount: Amount to be paid
    Returns:
        tuple: (is_valid, reason_message)
    """
    try:
        debt_amount = float(debt_amount)
        payment_amount = float(payment_amount)

        # Calculate absolute difference
        difference = abs(debt_amount - payment_amount)

        # Rule 1: If absolute difference <= 140 → accept (except when debt is zero)
        if difference <= 140 and debt_amount != 0:
            return True, f"تم قبول الدفع - الفرق ضمن الحد المسموح"

        # Rule 2: If debt <= payment amount → accept
        if debt_amount <= payment_amount:
            return True, f"تم قبول الدفع - المبلغ كافي لتغطية الدين"

        # Rule 3: If debt > payment amount and difference > 140 → reject
        if debt_amount > payment_amount:
            return False, f"{debt_amount:.0f}"

        return False, f"{debt_amount:.0f}"

    except Exception as e:
        log(f"Error validating debt payment: {e}", "ERROR")
        return False, f"خطأ غير معروف في معالجة بيانات الدين"

def check_amount_limit_inet(amount):
    """
    Check if payment amount exceeds the maximum limit of 100,000 SYP
    Args:
        amount: Payment amount to check
    Returns:
        tuple: (is_valid, error_message)
    """
    try:
        amount_value = float(amount)
        if amount_value > 100000:
            return False, f"المبلغ ({amount_value:.0f}) يتجاوز الحد الأقصى المسموح (100,000)"
        return True, None
    except Exception as e:
        return False, f"خطأ غير معروف في معالجة بيانات الدين"

def extract_package_price_inet(subscriber_info):
    """
    Extract package price from subscriber information
    Args:
        subscriber_info: Dictionary containing subscriber information
    Returns:
        float: Package price or -1 if extraction fails
    """
    try:
        if not subscriber_info or 'monthly_cost' not in subscriber_info:
            log("No subscriber info or monthly cost available", "ERROR")
            return -1.0

        monthly_cost_text = subscriber_info['monthly_cost']
        # Remove any non-numeric characters except decimal point
        import re
        price_match = re.search(r'(\d+(?:\.\d+)?)', monthly_cost_text.replace(',', ''))

        if price_match:
            price = float(price_match.group(1))
            log(f"Extracted package price: {price} from text: {monthly_cost_text}", "INFO")
            return price
        else:
            log(f"Could not extract price from: {monthly_cost_text}", "ERROR")
            return -1.0

    except Exception as e:
        log(f"Error extracting package price: {e}", "ERROR")
        return -1.0

def check_payment_amount_vs_package_price(payment_amount, package_price):
    """
    Check if payment amount is sufficient for the package price
    Args:
        payment_amount: Amount to be paid
        package_price: Required package price
    Returns:
        tuple: (is_sufficient, message)
    """
    try:
        payment_amount = float(payment_amount)
        package_price = float(package_price)

        log(f"Comparing payment amount {payment_amount} vs package price {package_price}", "INFO")

        if payment_amount >= package_price:
            log(f"Payment amount sufficient: {payment_amount} >= {package_price}", "INFO")
            return True, f"تم قبول الدفع - المبلغ كافي لسعر الحزمة"
        else:
            log(f"Payment amount insufficient: {payment_amount} < {package_price}", "WARNING")
            return False, f"{package_price:.0f}"

    except Exception as e:
        log(f"Error checking payment amount vs package price: {e}", "ERROR")
        return False, f"خطأ غير معروف في معالجة بيانات الدين"

def format_phone_number_inet(phone_number):
    """
    Format phone number for inet by adding '41' prefix if not already present
    Args:
        phone_number: Original phone number from API
    Returns:
        str: Formatted phone number with '41' prefix
    """
    try:
        # Convert to string and remove any whitespace
        phone_str = str(phone_number).strip()

        # Remove any leading zeros
        phone_str = phone_str.lstrip('0')

        # Check if already starts with '41'
        if phone_str.startswith('41'):
            formatted_number = phone_str
            log(f"Phone number already has '41' prefix: {phone_number} -> {formatted_number}", "INFO")
        else:
            # Add '41' prefix
            formatted_number = '41' + phone_str
            log(f"Added '41' prefix to phone number: {phone_number} -> {formatted_number}", "INFO")

        return formatted_number

    except Exception as e:
        log(f"Error formatting phone number {phone_number}: {e}", "ERROR")
        return str(phone_number)  # Return original if formatting fails



def display_api_request_summary_inet(order_id, status, message=None, payload=None):
    """
    Display API request details (automated version - no user confirmation)
    Args:
        order_id: Order ID
        status: Status code
        message: Optional message
        payload: Request payload
    Returns:
        bool: Always returns True (automatic processing)
    """
    status_descriptions = {
        1: "✅ PAID - Payment Successful",
        2: "❌ NUMBER NOT FOUND - Phone number not in system",
        3: "❌ PAYMENT FAILED - Payment processing error",
        4: "❌ SUBSCRIBER HAS DEBT - Customer owes company money"
    }

    print("\n" + "="*70)
    print("🌐 API REQUEST - AUTOMATED PROCESSING")
    print("="*70)
    print(f"📋 Order ID: {order_id}")
    print(f"📊 Status Code: {status}")
    print(f"📝 Status Description: {status_descriptions.get(status, 'Unknown status')}")
    if message:
        print(f"💬 Arabic Message: {message}")
    print(f"🌐 API Endpoint: [Disabled in standalone mode]")
    print(f"📦 Request Method: POST")

    if payload:
        print(f"📄 Payload Preview:")
        for key, value in payload.items():
            if key == "secret":
                print(f"   {key}: {'*' * len(str(value))}")
            else:
                print(f"   {key}: {value}")

    print("🤖 Automatically proceeding with API request...")
    log(f"Automatically processing API request for order {order_id} with status {status}", "INFO")
    return True



def display_payment_result_inet(order_id, status, message=None):
    """
    Display payment result to user (no API sending) - INET version
    Args:
        order_id: Order ID
        status: Status code (1=paid, 2=number not found, 3=payment failed, 4=subscriber has debt)
        message: Optional Arabic message for rejection cases
    """
    status_descriptions = {
        1: "✅ تم الدفع بنجاح",
        2: "❌ الرقم غير موجود في النظام",
        3: "❌ فشل في عملية الدفع",
        4: "❌ المشترك عليه دين"
    }

    print("\n" + "="*50)
    print("📋 نتيجة معالجة الطلب - INET")
    print("="*50)
    print(f"رقم الطلب: {order_id}")
    print(f"حالة الطلب: {status}")
    print(f"الوصف: {status_descriptions.get(status, 'حالة غير معروفة')}")
    if message:
        print(f"رسالة إضافية: {message}")
    print("="*50)

    log(f"INET payment result displayed for order {order_id} with status {status}", "INFO")

def perform_inet_login():
    """
    Step 1: Login to inet system
    Returns:
        tuple: (success, session, pg, papp) - success boolean, session object, and extracted parameters
    """
    try:
        log("Attempting to login to inet system...", "INFO")

        # Create a session to maintain cookies
        session = requests.Session()

        # inet login URL and credentials
        login_url = "https://pos.ispcare.inet.sy/"
        login_data = {
            'action': 'login',
            'username': 'nawraspos',
            'password': 'Idhm*0hgauvhx',
            'login': 'إرسال'
        }

        # Set headers to mimic the exact browser request
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'ar,en;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'pos.ispcare.inet.sy',
            'Origin': 'https://pos.ispcare.inet.sy',
            'Referer': 'https://pos.ispcare.inet.sy/',
            'Sec-CH-UA': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        # First, get the login page to establish session
        try:
            log("Getting login page first...", "INFO")
            get_response = session.get(login_url, headers=headers, verify=False)
            log(f"Login page response status: {get_response.status_code}", "INFO")
        except Exception as e:
            log(f"Warning: Could not get login page: {e}", "WARNING")

        log(f"Sending login request to: {login_url}", "INFO")
        log(f"Login username: {login_data['username']}", "INFO")

        # Send login request
        response = session.post(
            login_url,
            data=login_data,
            headers=headers,
            verify=False,
            allow_redirects=True
        )

        log(f"Login response status code: {response.status_code}", "INFO")
        log(f"Login response URL: {response.url}", "INFO")

        # Log cookies for debugging
        if session.cookies:
            log(f"Session cookies: {list(session.cookies.keys())}", "INFO")
            for cookie in session.cookies:
                log(f"Cookie: {cookie.name}={cookie.value}", "INFO")
        else:
            log("No cookies received", "WARNING")

        # Log response length for debugging
        log(f"Response content length: {len(response.text)} characters", "INFO")

        # Check if login was successful
        if response.status_code == 200:
            # Response debugging disabled in standalone mode
            log("Login response received (file saving disabled)", "INFO")

            # Check if we have a valid session cookie first (good sign of successful login)
            if session.cookies.get('PHPSESSID'):
                log("Found PHPSESSID cookie - login appears successful", "SUCCESS")

                # Look for positive indicators of successful login
                response_text = response.text
                success_indicators = [
                    'دفع فواتير',  # Payment bills link
                    'اشتراكاتي',   # My subscriptions
                    'papp=152',    # Payment app parameter
                    'dashboard',   # Dashboard content
                    'sidebar_menu' # Sidebar menu
                ]

                login_successful = False
                for indicator in success_indicators:
                    if indicator in response_text:
                        log(f"Login successful - found success indicator: {indicator}", "SUCCESS")
                        login_successful = True
                        break

                if not login_successful:
                    log("No success indicators found despite having session cookie", "WARNING")
                    # Still proceed since we have a valid session cookie
                    login_successful = True

            else:
                log("No PHPSESSID cookie found - login failed", "ERROR")
                return False, None, None, None

            # Extract href from the payment bills link
            try:
                import re

                soup = BeautifulSoup(response.text, 'html.parser')

                # Look for the payment bills link with multiple patterns
                payment_link = None

                # Pattern 1: Look for links containing "دفع فواتير" text
                all_links = soup.find_all('a', href=True)
                for link in all_links:
                    if 'دفع فواتير' in link.get_text(strip=True):
                        payment_link = link
                        log("Found payment link by text 'دفع فواتير'", "SUCCESS")
                        break

                # Pattern 2: Look for href containing papp=152
                if not payment_link:
                    payment_link = soup.find('a', href=re.compile(r'papp=152'))
                    if payment_link:
                        log("Found payment link by papp=152 pattern", "SUCCESS")

                # Pattern 3: Look for any link with pg and papp parameters
                if not payment_link:
                    payment_link = soup.find('a', href=re.compile(r'\?.*pg=.*papp='))
                    if payment_link:
                        log("Found payment link by pg/papp pattern", "SUCCESS")

                # Pattern 4: Look for any link containing "bill" or "فاتورة"
                if not payment_link:
                    all_links = soup.find_all('a', href=True)
                    for link in all_links:
                        link_text = link.get_text(strip=True).lower()
                        href = link.get('href', '').lower()
                        if any(keyword in link_text for keyword in ['فاتورة', 'فواتير', 'bill', 'payment']) or \
                           any(keyword in href for keyword in ['bill', 'payment', 'papp']):
                            payment_link = link
                            log(f"Found payment link by keyword search: {link_text}", "SUCCESS")
                            break

                if payment_link:
                    href = payment_link.get('href')
                    log(f"Found payment bills link: {href}", "SUCCESS")

                    # Clean up href (handle HTML entities)
                    href_clean = href.replace('&amp;', '&')
                    log(f"Cleaned href: {href_clean}", "INFO")

                    # Extract pg and papp parameters
                    pg_match = re.search(r'pg=(\d+)', href_clean)
                    papp_match = re.search(r'papp=(\d+)', href_clean)

                    if pg_match and papp_match:
                        pg = pg_match.group(1)
                        papp = papp_match.group(1)
                        log(f"Extracted parameters - pg: {pg}, papp: {papp}", "SUCCESS")
                        return True, session, pg, papp
                    else:
                        log(f"Could not extract pg and papp parameters from href: {href_clean}", "WARNING")
                        # Try to use default values since we have a valid session
                        log("Using default values: pg=1, papp=152", "INFO")
                        return True, session, "1", "152"
                else:
                    log("Could not find payment bills link in response", "WARNING")
                    # If we have a valid session cookie, assume login was successful
                    if session.cookies.get('PHPSESSID'):
                        log("Have valid session cookie - assuming login successful", "SUCCESS")
                        log("Using default values: pg=1, papp=152", "INFO")
                        return True, session, "1", "152"
                    else:
                        log("No session cookie and no payment link found - login failed", "ERROR")
                        return False, None, None, None

            except Exception as e:
                log(f"Error parsing login response: {e}", "ERROR")
                return False, None, None, None
        else:
            log(f"Login failed - HTTP status code: {response.status_code}", "ERROR")
            return False, None, None, None

    except requests.exceptions.RequestException as e:
        log(f"Network error during inet login: {e}", "ERROR")
        return False, None, None, None
    except Exception as e:
        log(f"Unexpected error during inet login: {e}", "ERROR")
        return False, None, None, None

def check_gateway_balance_inet(session):
    """
    Step 2: Check gateway balance from the main dashboard
    Args:
        session: Authenticated requests session
    Returns:
        float: Gateway balance amount, or -1 if check fails
    """
    try:
        log("Checking inet gateway balance...", "INFO")

        # Get the main dashboard page to extract balance
        dashboard_url = "https://pos.ispcare.inet.sy/"

        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'ar,en;q=0.9',
            'Connection': 'keep-alive',
            'Host': 'pos.ispcare.inet.sy',
            'Sec-CH-UA': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        response = session.get(dashboard_url, headers=headers, verify=False)

        if response.status_code == 200:
            # HTML saving disabled in standalone mode
            log("Dashboard response received (HTML saving disabled)", "INFO")

            # Extract balance from the dashboard
            balance = extract_gateway_balance_from_dashboard(response.text)
            if balance is not None:
                log(f"Gateway balance extracted: {balance}", "SUCCESS")
                return balance
            else:
                log("Could not extract gateway balance", "WARNING")
                return -1  # Return -1 to indicate extraction failure
        else:
            log(f"Failed to get dashboard - HTTP {response.status_code}", "ERROR")
            return -1  # Return -1 to indicate request failure

    except Exception as e:
        log(f"Error checking gateway balance: {e}", "ERROR")
        return -1  # Return -1 to indicate error

def extract_gateway_balance_from_dashboard(html_content):
    """
    Extract gateway balance from dashboard HTML
    Args:
        html_content: HTML content from dashboard
    Returns:
        float: Gateway balance or None if not found
    """
    try:
        import re

        soup = BeautifulSoup(html_content, 'html.parser')

        # Look for balance in the sidebar table
        # The balance is typically in: /html/body/div[2]/div[1]/aside/div[1]/table/tbody/tr[2]/td[2]
        # Or search for table cells containing balance information

        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 2:
                    # Look for balance-related text in first cell
                    first_cell_text = cells[0].get_text(strip=True).lower()
                    if any(keyword in first_cell_text for keyword in ['رصيد', 'balance', 'الرصيد']):
                        # Extract balance from second cell
                        balance_text = cells[1].get_text(strip=True)
                        balance_match = re.search(r'([\d,.-]+)', balance_text.replace(',', ''))
                        if balance_match:
                            try:
                                balance = float(balance_match.group(1))
                                log(f"Found gateway balance: {balance} from text: {balance_text}", "SUCCESS")
                                return balance
                            except ValueError:
                                continue

        log("Could not find gateway balance in dashboard", "WARNING")
        return None

    except Exception as e:
        log(f"Error extracting gateway balance: {e}", "ERROR")
        return None

def search_phone_number_inet(session, phone_number, pg, papp):
    """
    Step 3: Search for phone number in inet system
    Args:
        session: Authenticated requests session
        phone_number: Formatted phone number to search for
        pg: Page parameter from login
        papp: Application parameter from login
    Returns:
        tuple: (subscriber_info, sub_id) - subscriber information and sub_id for next request
    """
    try:
        log(f"Searching for phone number in inet system: {phone_number}", "INFO")

        # inet search URL with dynamic parameters
        search_url = f"https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}"

        # Search form data with dynamic parameters
        search_data = {
            'pg': pg,
            'papp': papp,
            'pa': 'list',
            'subscription_id_pat': '=',
            'subscription_id': '',
            'subscription_fname_pat': '=',
            'subscription_fname': '',
            'subscription_lname_pat': '=',
            'subscription_lname': '',
            'subscription_phone_pat': '=',
            'subscription_phone': phone_number,
            'submit_1_152': 'بحث'
        }

        # Set headers to mimic the exact browser request
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'ar,en;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'pos.ispcare.inet.sy',
            'Origin': 'https://pos.ispcare.inet.sy',
            'Referer': f'https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}',
            'Sec-CH-UA': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        log(f"Sending search request for phone: {phone_number}", "INFO")
        log(f"Search URL: {search_url}", "INFO")

        # Send search request using the authenticated session
        response = session.post(
            search_url,
            data=search_data,
            headers=headers,
            verify=False,
            allow_redirects=True
        )

        log(f"Search response status code: {response.status_code}", "INFO")
        log(f"Search response URL: {response.url}", "INFO")

        if response.status_code == 200:
            # HTML saving disabled in standalone mode
            log("Search response received (HTML saving disabled)", "INFO")

            # فحص وجود رسالة الانتظار 5 دقائق (خطأ تقني)
            if "الرجاء الانتظار 5 دقائق بين الدفعات المتتالية" in response.text:
                log(f"Technical error detected: 5-minute wait message found for phone {phone_number}", "ERROR")
                # إرجاع قيمة خاصة للإشارة إلى الخطأ التقني
                return "TECHNICAL_ERROR_5MIN_WAIT", None

            # Parse the HTML response to extract subscriber information and sub_id
            subscriber_info = extract_subscriber_info_inet(response.text)

            # Extract sub_id from the response
            sub_id = extract_sub_id_from_search_response(response.text)

            if subscriber_info and sub_id:
                log(f"Subscriber found: {subscriber_info.get('name', 'N/A')} - {subscriber_info.get('phone_number', 'N/A')}, sub_id: {sub_id}", "SUCCESS")
                return subscriber_info, sub_id
            else:
                log(f"No subscriber found or sub_id missing for phone number: {phone_number}", "WARNING")
                return None, None
        else:
            log(f"Search request failed - HTTP status code: {response.status_code}", "ERROR")
            return None, None

    except requests.exceptions.RequestException as e:
        log(f"Network error during phone number search: {e}", "ERROR")
        return None, None
    except Exception as e:
        log(f"Unexpected error during phone number search: {e}", "ERROR")
        return None, None

def extract_sub_id_from_search_response(html_content):
    """
    Extract sub_id from search response HTML
    Args:
        html_content: HTML content from search response
    Returns:
        str: sub_id or None if not found
    """
    try:
        import re

        soup = BeautifulSoup(html_content, 'html.parser')

        # Method 1: Look for sub_id in href attributes of links
        links = soup.find_all('a', href=True)
        for link in links:
            href = link.get('href')
            sub_id_match = re.search(r'sub_id=(\d+)', href)
            if sub_id_match:
                sub_id = sub_id_match.group(1)
                log(f"Extracted sub_id from link href: {sub_id}", "SUCCESS")
                return sub_id

        # Method 2: Look for sub_id in form inputs
        inputs = soup.find_all('input', {'name': 'sub_id'})
        for input_tag in inputs:
            value = input_tag.get('value')
            if value and value.isdigit():
                log(f"Extracted sub_id from form input: {value}", "SUCCESS")
                return value

        # Method 3: Search for sub_id pattern in all HTML content
        sub_id_pattern = re.compile(r'sub_id[=:](\d+)')
        match = sub_id_pattern.search(html_content)
        if match:
            sub_id = match.group(1)
            log(f"Extracted sub_id from HTML content: {sub_id}", "SUCCESS")
            return sub_id

        # Method 4: Look in table cells for subscriber ID
        # Sometimes the subscriber ID is displayed in the first column
        subscriber_row = soup.find('tr', class_='dark')
        if subscriber_row:
            cells = subscriber_row.find_all('td')
            if cells:
                # Check if first cell contains a numeric ID
                first_cell_text = cells[0].get_text(strip=True)
                if first_cell_text.isdigit():
                    log(f"Extracted sub_id from table cell: {first_cell_text}", "SUCCESS")
                    return first_cell_text

                # Check for links in first cell
                first_cell_links = cells[0].find_all('a', href=True)
                for link in first_cell_links:
                    href = link.get('href')
                    sub_id_match = re.search(r'sub_id=(\d+)', href)
                    if sub_id_match:
                        sub_id = sub_id_match.group(1)
                        log(f"Extracted sub_id from table cell link: {sub_id}", "SUCCESS")
                        return sub_id

        log("Could not extract sub_id from search response", "ERROR")
        return None

    except Exception as e:
        log(f"Error extracting sub_id: {e}", "ERROR")
        return None

def validate_subscriber_debt_inet(subscriber_info, payment_amount):
    """
    Step 4: Validate subscriber debt status and payment amount
    Args:
        subscriber_info: Subscriber information from search
        payment_amount: Amount to be paid
    Returns:
        dict: Validation result with is_valid and message
    """
    try:
        log("Validating subscriber debt status...", "INFO")

        if not subscriber_info:
            return {'is_valid': False, 'message': 'معلومات المشترك غير متوفرة'}

        # Check if subscriber has debt based on balance color
        if subscriber_info.get('has_debt', False):
            debt_amount = subscriber_info.get('debt_amount', 0)
            is_valid, message = validate_debt_payment_inet(debt_amount, payment_amount)
            return {'is_valid': is_valid, 'message': message}
        else:
            # No debt detected - allow payment (package price check will be done separately for MB orders only)
            return {'is_valid': True, 'message': 'لا توجد ديون - السماح بالدفع'}

    except Exception as e:
        log(f"Error validating subscriber debt: {e}", "ERROR")
        return {'is_valid': False, 'message': f'خطأ في التحقق من الدين: {str(e)}'}

def get_payment_form_data_inet(session, sub_id, pg, papp):
    """
    Step 4: Get payment form data by making GET request to add_payment page
    Args:
        session: Authenticated requests session
        sub_id: Subscriber ID from search
        pg: Page parameter
        papp: Application parameter
    Returns:
        dict: Payment form data (faccount_no, code, dfaccount_no, transid) or None if failed
    """
    try:
        log(f"Getting payment form data for sub_id: {sub_id}", "INFO")

        # Payment form URL
        payment_form_url = f"https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}&pa=add_payment&sub_id={sub_id}"

        # Set headers for GET request
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'ar,en;q=0.9',
            'Connection': 'keep-alive',
            'Host': 'pos.ispcare.inet.sy',
            'Referer': f'https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}',
            'Sec-CH-UA': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        log(f"Sending GET request to payment form: {payment_form_url}", "INFO")

        # Send GET request to payment form
        response = session.get(
            payment_form_url,
            headers=headers,
            verify=False,
            allow_redirects=True
        )

        log(f"Payment form response status code: {response.status_code}", "INFO")

        if response.status_code == 200:
            # HTML saving disabled in standalone mode
            log("Payment form response received (HTML saving disabled)", "INFO")

            # Extract payment form data
            form_data = extract_payment_form_data(response.text)
            if form_data:
                log(f"Extracted payment form data: {form_data}", "SUCCESS")
                return form_data
            else:
                log("Could not extract payment form data", "ERROR")
                return None
        else:
            log(f"Payment form request failed - HTTP status code: {response.status_code}", "ERROR")
            return None

    except requests.exceptions.RequestException as e:
        log(f"Network error during payment form request: {e}", "ERROR")
        return None
    except Exception as e:
        log(f"Unexpected error during payment form request: {e}", "ERROR")
        return None

def extract_payment_form_data(html_content):
    """
    Extract payment form data from HTML response
    Args:
        html_content: HTML content from payment form
    Returns:
        dict: Form data with faccount_no, code, dfaccount_no, transid
    """
    try:
        import re

        soup = BeautifulSoup(html_content, 'html.parser')

        form_data = {}

        # Extract hidden form fields and input values
        inputs = soup.find_all('input')
        for input_tag in inputs:
            name = input_tag.get('name', '')
            value = input_tag.get('value', '')

            if name in ['faccount_no', 'code', 'dfaccount_no', 'transid', 'sub_id']:
                form_data[name] = value
                log(f"Extracted {name}: {value}", "INFO")

        # Verify we have all required fields
        required_fields = ['faccount_no', 'code', 'dfaccount_no', 'transid']
        for field in required_fields:
            if field not in form_data:
                log(f"Missing required field: {field}", "ERROR")
                return None

        return form_data

    except Exception as e:
        log(f"Error extracting payment form data: {e}", "ERROR")
        return None

def perform_first_payment_confirmation_inet(session, form_data, amount, pg, papp):
    """
    Step 5: Perform first payment confirmation
    Args:
        session: Authenticated requests session
        form_data: Payment form data from previous step
        amount: Payment amount
        pg: Page parameter
        papp: Application parameter
    Returns:
        bool: True if confirmation successful, False otherwise
    """
    try:
        log("Performing first payment confirmation...", "INFO")

        # First confirmation URL
        confirmation_url = f"https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}"

        # First confirmation payload
        confirmation_data = {
            'pg': pg,
            'papp': papp,
            'sub_id': form_data.get('sub_id'),
            'faccount_no': form_data.get('faccount_no'),
            'code': form_data.get('code'),
            'pa': 'add_payment',
            'dfaccount_no': form_data.get('dfaccount_no'),
            'amount': amount,
            'transid': form_data.get('transid'),
            'notes': '',
            'submit_1_152': 'إرسال'
        }

        # Set headers for POST request
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'ar,en;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'pos.ispcare.inet.sy',
            'Origin': 'https://pos.ispcare.inet.sy',
            'Referer': f'https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}&pa=add_payment&sub_id={form_data.get("sub_id")}',
            'Sec-CH-UA': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        log(f"Sending first confirmation request with amount: {amount}", "INFO")

        # Send first confirmation request
        response = session.post(
            confirmation_url,
            data=confirmation_data,
            headers=headers,
            verify=False,
            allow_redirects=True
        )

        log(f"First confirmation response status code: {response.status_code}", "INFO")

        if response.status_code == 200:
            # HTML saving disabled in standalone mode
            log("First confirmation response received (HTML saving disabled)", "INFO")
            return True
        else:
            log(f"First confirmation failed - HTTP status code: {response.status_code}", "ERROR")
            return False

    except Exception as e:
        log(f"Error during first confirmation: {e}", "ERROR")
        return False

def perform_final_payment_confirmation_inet(session, form_data, amount, pg, papp):
    """
    Step 6: Perform final payment confirmation
    Args:
        session: Authenticated requests session
        form_data: Payment form data
        amount: Payment amount
        pg: Page parameter
        papp: Application parameter
    Returns:
        tuple: (success, new_balance) - success boolean and new balance if available
    """
    try:
        log("Performing final payment confirmation...", "INFO")

        # Final confirmation URL
        confirmation_url = f"https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}"

        # Final confirmation payload
        confirmation_data = {
            'pg': pg,
            'papp': papp,
            'sub_id': form_data.get('sub_id'),
            'faccount_no': form_data.get('faccount_no'),
            'code': form_data.get('code'),
            'pa': 'add_do_payment',
            'number': form_data.get('transid'),
            'amount': amount,
            'notes': '',
            'transid': form_data.get('transid'),
            'dfaccount_no': form_data.get('dfaccount_no'),
            'submit_1_152': 'تأكيد'
        }

        # Set headers for POST request
        headers = {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'ar,en;q=0.9',
            'Cache-Control': 'max-age=0',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': 'pos.ispcare.inet.sy',
            'Origin': 'https://pos.ispcare.inet.sy',
            'Referer': f'https://pos.ispcare.inet.sy/?&pg={pg}&papp={papp}',
            'Sec-CH-UA': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }

        log(f"Sending final confirmation request with amount: {amount}", "INFO")

        # Send final confirmation request
        response = session.post(
            confirmation_url,
            data=confirmation_data,
            headers=headers,
            verify=False,
            allow_redirects=True
        )

        log(f"Final confirmation response status code: {response.status_code}", "INFO")

        if response.status_code == 200:
            # HTML saving disabled in standalone mode
            log("Final confirmation response received (HTML saving disabled)", "INFO")

            # Extract new balance from response
            new_balance = extract_new_balance_from_response(response.text)
            if new_balance is not None:
                log(f"Extracted new balance: {new_balance}", "SUCCESS")
                return True, new_balance
            else:
                log("Could not extract new balance from response", "WARNING")
                return True, None
        else:
            log(f"Final confirmation failed - HTTP status code: {response.status_code}", "ERROR")
            return False, None

    except Exception as e:
        log(f"Error during final confirmation: {e}", "ERROR")
        return False, None

def extract_new_balance_from_response(html_content):
    """
    Extract new balance from final confirmation response using the same method as dashboard balance extraction
    Args:
        html_content: HTML content from final confirmation response
    Returns:
        float: New balance or None if not found
    """
    try:
        import re

        soup = BeautifulSoup(html_content, 'html.parser')

        # Use the same successful method as extract_gateway_balance_from_dashboard
        # Look for balance in all tables, searching for balance-related keywords
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 2:
                    # Look for balance-related text in first cell
                    first_cell_text = cells[0].get_text(strip=True).lower()
                    if any(keyword in first_cell_text for keyword in ['رصيد', 'balance', 'الرصيد', 'الرصيد الحالي']):
                        # Extract balance from second cell
                        balance_text = cells[1].get_text(strip=True)
                        balance_match = re.search(r'([\d,.-]+)', balance_text.replace(',', ''))
                        if balance_match:
                            try:
                                balance = float(balance_match.group(1))
                                log(f"Found new balance: {balance} from text: {balance_text}", "SUCCESS")
                                return balance
                            except ValueError:
                                continue

        # Fallback: Look for balance information in any table cells (original method as backup)
        # The balance might be in a cell that follows a cell containing balance-related text
        cells = soup.find_all('td')
        for i, cell in enumerate(cells):
            cell_text = cell.get_text(strip=True)
            # More flexible search for balance-related text
            if any(keyword in cell_text for keyword in ['الرصيد الحالي', 'الرصيد', 'رصيد']) and any(currency in cell_text for currency in ['ل.س', 'ل س', 'SYP']):
                # The next cell should contain the balance
                if i + 1 < len(cells):
                    balance_cell = cells[i + 1]
                    balance_text = balance_cell.get_text(strip=True)

                    # Extract numeric value from balance text
                    balance_match = re.search(r'([\d,.-]+)', balance_text.replace(',', ''))
                    if balance_match:
                        try:
                            balance = float(balance_match.group(1))
                            log(f"Found new balance (fallback method): {balance} from text: {balance_text}", "SUCCESS")
                            return balance
                        except ValueError:
                            log(f"Could not convert balance to float: {balance_match.group(1)}", "ERROR")
                            continue

        log("Could not find balance information in response", "WARNING")
        return None

    except Exception as e:
        log(f"Error extracting new balance: {e}", "ERROR")
        return None

def display_payment_process_overview(order, amount):
    """
    Display overview of all payment process steps before starting
    Args:
        order: Order data from API
        amount: Payment amount
    Returns:
        bool: True if user wants to proceed, False otherwise
    """
    formatted_phone = format_phone_number_inet(order['phone_number'])

    print("\n" + "="*80)
    print("🔄 inet PAYMENT PROCESS OVERVIEW")
    print("="*80)
    print(f"📋 Order ID: {order.get('id')}")
    print(f"📱 Phone Number: {order['phone_number']} → {formatted_phone}")
    print(f"💰 Payment Amount: {amount} SYP")
    print(f"📦 Order Type: {order.get('type', 'N/A')}")
    print("\n📋 PROCESS STEPS THAT WILL BE EXECUTED:")
    print("   1️⃣ Get Login Page (GET request)")
    print("   2️⃣ Login to inet System (POST request)")
    print("   3️⃣ Check Gateway Balance (GET request)")
    print("   4️⃣ Search Phone Number (POST request)")
    print("   5️⃣ Get Payment Form Data (GET request)")
    print("   6️⃣ First Payment Confirmation (POST request)")
    print("   7️⃣ Final Payment Confirmation (POST request)")
    print("\n⚠️ IMPORTANT NOTES:")
    print("   • You will be asked to confirm EACH request before it's sent")
    print("   • You can cancel at any step by choosing 'N'")
    print("   • Step 7 is the FINAL step that completes the payment")
    print("   • All requests will be logged for debugging")
    print("="*80)

    # Automatically proceed (no user confirmation needed)
    print("🤖 Automatically proceeding...")
    return True
def display_payment_summary_inet(order, amount, duplicate_info=None, debt_info=None, subscriber_info=None, package_info=None):
    """
    Display detailed payment summary for inet and ask for user confirmation (like Sawa system)
    Args:
        order: Order data from API
        amount: Payment amount
        duplicate_info: Information about duplicate detection
        debt_info: Information about subscriber debt status
        subscriber_info: Information about subscriber from HTML parsing
        package_info: Information about package price validation
    Returns:
        bool: True if user confirms payment, False otherwise
    """
    print("\n" + "="*80)
    print("🧾 inet PAYMENT SUMMARY - PLEASE REVIEW BEFORE PROCEEDING")
    print("="*80)
    print("⚠️ NOTE: Payment will go through multiple verification steps:")
    print("   1️⃣ Login to inet system")
    print("   2️⃣ Check gateway balance")
    print("   3️⃣ Search for phone number")
    print("   4️⃣ Validate debt status")
    print("   5️⃣ First confirmation")
    print("   6️⃣ Second confirmation")
    print("   7️⃣ Verify payment by balance change")
    print("="*80)

    # Basic order information
    print(f"📋 Order ID: {order.get('id')}")
    print(f"📱 Original Phone: {order['phone_number']}")
    # Show formatted phone number if different
    formatted_phone = format_phone_number_inet(order['phone_number'])
    if formatted_phone != str(order['phone_number']):
        print(f"📞 Formatted Phone: {formatted_phone}")
    print(f"💰 Payment Amount: {amount} SYP")
    print(f"📦 Order Type: {order.get('type', 'N/A')}")

    # Subscriber information (if available)
    if subscriber_info:
        print(f"\n👤 SUBSCRIBER INFORMATION:")
        print(f"📝 Name: {subscriber_info.get('name', 'N/A')}")
        print(f"📞 Phone: {subscriber_info.get('phone_number', 'N/A')}")
        print(f"🔧 Service Type: {subscriber_info.get('service_type', 'N/A')}")
        print(f"📊 Search Status: {subscriber_info.get('search_status', 'N/A')}")
        print(f"📦 Subscription: {subscriber_info.get('subscription_type', 'N/A')}")
        print(f"💳 Monthly Cost: {subscriber_info.get('monthly_cost', 'N/A')} SYP")
        print(f"💰 Balance: {subscriber_info.get('balance_text', 'N/A')} (Color: {subscriber_info.get('balance_color', 'N/A')})")

    # Package price validation (if available)
    if package_info:
        print(f"\n💰 PACKAGE PRICE VALIDATION:")
        print(f"📦 Package Price: {package_info.get('package_price', 'N/A')} SYP")
        print(f"💳 Payment Amount: {amount} SYP")
        if package_info.get('is_sufficient'):
            print(f"✅ {package_info.get('message', 'Payment amount is sufficient')}")
        else:
            print(f"❌ {package_info.get('message', 'Payment amount is insufficient')}")

    # Duplicate information
    if duplicate_info:
        print(f"🔄 Duplicate Status: {duplicate_info['status']}")
        if duplicate_info['status'] == 'Found within 5 minutes':
            print("❌ Recent payment found - PAYMENT WILL BE REJECTED")
        else:
            print("✅ No recent duplicate found - payment allowed")

    # Debt information
    if debt_info:
        print(f"\n💳 DEBT STATUS:")
        print(f"📊 Status: {debt_info['status']}")
        if debt_info.get('has_debt') is None:
            print("⚠️ Debt status unknown - will be checked after login and search")
        elif debt_info.get('has_debt', False):
            print(f"� Debt Amount: {debt_info.get('debt_amount', 0)} SYP")
            print(f"🎨 Balance Color: {debt_info.get('balance_color', 'N/A')}")
            if debt_info.get('validation_passed', False):
                print(f"✅ Payment validation: {debt_info.get('validation_message', 'Payment amount sufficient')}")
            else:
                print(f"❌ Payment validation: {debt_info.get('validation_message', 'Payment amount insufficient')} - PAYMENT WILL BE REJECTED")
        else:
            balance_color = debt_info.get('balance_color', 'N/A')
            print(f"✅ No debt detected - Balance color: {balance_color} - payment allowed")

    # Validation checks
    print("\n📊 VALIDATION CHECKS:")
    print("✅ No amount limits for inet - all amounts accepted")

    # Package price validation (only for MB orders)
    if "MB" in order.get('type', ''):
        if package_info:
            if package_info.get('is_sufficient'):
                print("✅ Payment amount sufficient for package price")
            else:
                print("❌ Payment amount insufficient for package price - PAYMENT WILL BE REJECTED")
        else:
            print("⚠️ Package price validation will be performed after search")
    else:
        print("ℹ️ Non-MB order - package price validation not required")

    # Duplicate validation
    if duplicate_info and duplicate_info['status'] == 'Found within 5 minutes':
        print("❌ Recent payment found within 5 minutes - PAYMENT WILL BE REJECTED")
    else:
        print("✅ No recent duplicate payments found")

    # Debt validation
    if debt_info and debt_info.get('has_debt') is None:
        print("⚠️ Debt status will be verified after login and number search")
    elif debt_info and debt_info.get('has_debt', False):
        if debt_info.get('validation_passed', False):
            print("✅ Payment amount sufficient to cover debt")
        else:
            print("❌ Payment amount insufficient to cover debt - PAYMENT WILL BE REJECTED")
    else:
        print("✅ No debt issues detected")

    print("\n" + "="*80)

    # Ask for confirmation
    # Automatically proceed (no user confirmation needed)
    print("🤖 Automatically proceeding...")
    return True
def display_api_request_summary_inet(order_id, status, message=None, payload=None):
    """
    Display inet API request details and get user confirmation (like Sawa system)
    Args:
        order_id: Order ID
        status: Status code
        message: Optional message
        payload: Request payload
    Returns:
        bool: True if user confirms, False if user skips
    """
    status_descriptions = {
        1: "✅ PAID - Payment Successful",
        2: "❌ NUMBER NOT FOUND - Phone number not in system",
        3: "❌ PAYMENT FAILED - Payment processing error",
        4: "❌ SUBSCRIBER HAS DEBT - Customer owes company money"
    }

    while True:
        print("\n" + "="*70)
        print("🌐 inet API REQUEST CONFIRMATION")
        print("="*70)
        print(f"📋 Order ID: {order_id}")
        print(f"📊 Status Code: {status}")
        print(f"📝 Status Description: {status_descriptions.get(status, 'Unknown status')}")
        if message:
            print(f"💬 Arabic Message: {message}")
        print(f"🔗 API Endpoint: [Disabled in standalone mode]")
        print(f"📦 Request Method: POST")
        print(f"🏢 Company: inet Syria")

        if payload:
            print(f"📄 Payload Preview:")
            for key, value in payload.items():
                if key == "secret":
                    print(f"   {key}: {'*' * len(str(value))}")
                else:
                    print(f"   {key}: {value}")

        print("\n🤔 Do you want to send this inet API request?")
        print("   [Y] Yes - Send API request")
        print("   [N] No - Skip API request")
        print("   [S] Show Details - Display additional information")
        print("   [Q] Quit - Exit program")
        print("="*70)

        try:
            choice = "Y"  # input("👉 Enter your choice (Y/N/S/Q): ").strip().upper()

            if choice == 'Y':
                log(f"User confirmed inet API request for order {order_id} with status {status}", "INFO")
                return True
            elif choice == 'N':
                log(f"User skipped inet API request for order {order_id} with status {status}", "WARNING")
                return False
            elif choice == 'S':
                # Show additional details
                print("\n📋 ADDITIONAL inet API REQUEST DETAILS:")
                print(f"🕐 Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"🌐 Target Server: menfax.com")
                print(f"🔒 Authentication: Secret key (hidden)")
                print(f"📡 Request Timeout: 10 seconds")
                print(f"🔄 Retry Policy: No automatic retry")
                print(f"📊 Expected Response: JSON with confirmation")
                print(f"🏢 Company: inet Syria (same API as Sawa)")
                if status == 1:
                    print(f"💰 Action: Mark inet payment as successful in remote system")
                else:
                    print(f"⚠️ Action: Report inet payment failure/rejection to remote system")
                continue
            elif choice == 'Q':
                log("User chose to quit inet program during API confirmation", "WARNING")
                print("👋 Exiting program...")
                sys.exit()
            else:
                print("❌ Invalid choice. Please enter Y, N, S, or Q.")
                continue

        except KeyboardInterrupt:
            print("\n\n👋 Program interrupted by user")
            log("inet program interrupted by user during API confirmation", "WARNING")
            sys.exit()
        except Exception as e:
            print(f"❌ Error getting user input: {e}")
            log(f"Error getting user input during inet API confirmation: {e}", "ERROR")
            continue



def process_single_payment_inet(order):
    """
    Process a single inet payment order with enhanced error handling and validation
    Args:
        order: Order data from user input
    Returns:
        bool: True if payment was processed successfully, False otherwise
    """
    order_type = order.get("type", "")
    original_number = order["phone_number"]
    number = format_phone_number_inet(original_number)  # Format phone number with '41' prefix
    order_id = str(order.get("id"))
    amount = order["amount"]

    log(f"Processing inet order ID: {order_id}, Original Phone: {original_number}, Formatted Phone: {number}, Amount: {amount}", "INFO")

    try:
        # التحقق من الحد الأقصى للمبلغ (100,000 ليرة سورية)
        is_amount_valid, amount_error = check_amount_limit_inet(amount)
        if not is_amount_valid:
            log(f"Amount {amount} exceeds maximum limit of 100,000", "ERROR")
            log_payment_details(order_id, number, amount, "3", f"Amount exceeds maximum limit: {amount} > 100,000")
            display_payment_result_inet(order_id, 3, amount_error)
            return False

        # Check for duplicate payment within 5 minutes (inet specific)
        if check_duplicate_payment_inet(number, amount):
            log(f"Duplicate inet payment detected for {number} with amount {amount} within 5 minutes", "WARNING")
            log_payment_details(order_id, number, amount, "3", "Duplicate payment within 5 minutes")
            display_payment_result_inet(order_id, 3, "تم العثور على دفعة مماثلة خلال آخر 5 دقائق")
            return False

        # Step 1: Login to inet system
        log("Step 1: Logging into inet system...", "INFO")
        success, session, pg, papp = perform_inet_login()
        if not success:
            log(f"inet login failed for order {order_id}", "ERROR")
            log_payment_details(order_id, number, amount, "3", "inet login failed")
            display_payment_result_inet(order_id, 3, "خطأ في معالجة الطلب: فشل في تسجيل الدخول إلى نظام inet")
            return False

        # Step 2: Check gateway balance before payment
        log("Step 2: Checking inet gateway balance before payment...", "INFO")
        gateway_balance_before = check_gateway_balance_inet(session)
        if gateway_balance_before == -1:
            log("Failed to extract gateway balance before payment", "WARNING")
        else:
            log(f"Gateway balance before payment: {gateway_balance_before}", "INFO")

        # Step 3: Search for phone number
        log(f"Step 3: Searching for phone number {number} in inet system...", "INFO")
        subscriber_info, sub_id = search_phone_number_inet(session, number, pg, papp)

        # فحص الخطأ التقني (رسالة الانتظار 5 دقائق)
        if subscriber_info == "TECHNICAL_ERROR_5MIN_WAIT":
            log(f"Technical error: 5-minute wait message detected for phone {number}", "ERROR")
            log_payment_details(order_id, number, amount, "3", "Technical error: 5-minute wait between payments")
            display_payment_result_inet(order_id, 3, "خطأ تقني: الرجاء الانتظار 5 دقائق بين الدفعات المتتالية")
            return False

        # التحقق من وجود الرقم والكلفة الشهرية - إذا لم نجد معلومات المشترك أو لم نجد الكلفة الشهرية
        package_price_value = -1
        if subscriber_info and sub_id:
            package_price_value = extract_package_price_inet(subscriber_info)

        # إذا لم نجد معلومات المشترك أو لم نجد الكلفة الشهرية = خطأ في الرقم أو الشركة
        if not subscriber_info or not sub_id or package_price_value <= 0:
            log(f"Phone number {number} not found or invalid - wrong number or company", "ERROR")
            log_payment_details(order_id, number, amount, "2", "Phone number not found or invalid - wrong number or company")
            display_payment_result_inet(order_id, 2, "خطأ الرقم أو الشركة")
            return False

        # التحقق من الدين أولاً - إذا كان هناك دين ولا يطابق المبلغ المطلوب، يرسل "المبلغ المطلوب" + الرقم
        log("Step 4: Checking subscriber debt status...", "INFO")
        if subscriber_info.get('has_debt', False):
            debt_amount = subscriber_info.get('debt_amount', 0)
            is_debt_valid, debt_message = validate_debt_payment_inet(debt_amount, amount)

            if not is_debt_valid:
                log(f"Payment rejected due to debt: {debt_message}", "ERROR")
                # إرسال "المبلغ المطلوب" متبوعاً بمبلغ الدين
                debt_message = f"المبلغ المطلوب {int(debt_amount)}"
                log_payment_details(order_id, number, amount, "4", f"Subscriber has debt: {debt_amount}")
                display_payment_result_inet(order_id, 4, debt_message)
                return False

        # التحقق من مطابقة سعر الحزمة للطلبات من نوع MB
        if "MB" in order_type:
            log("Checking package price for MB order", "INFO")

            # التحقق من كفاية المبلغ لسعر الحزمة - إذا كان المبلغ أقل من سعر الحزمة، يرسل "المبلغ المطلوب" + الرقم
            if int(amount) < int(package_price_value):
                log(f"Payment amount ({amount}) is less than package price ({package_price_value})", "WARNING")
                # إرسال "المبلغ المطلوب" متبوعاً بكلفة الحزمة الشهرية
                package_message = f"المبلغ المطلوب {int(package_price_value)}"
                log_payment_details(order_id, number, amount, "4", f"Amount less than package price: {amount} < {package_price_value}")
                display_payment_result_inet(order_id, 4, package_message)
                return False

            # إذا كان المبلغ يطابق سعر الحزمة، يجب التحقق من الدين مرة أخرى
            log(f"Payment amount matches package price - proceeding with debt verification", "INFO")

        # إذا وصلنا هنا، فالدفع مقبول - نتابع عملية الدفع
        log("All validations passed - proceeding with payment", "INFO")

        # Prepare information for display
        duplicate_info = {
            'status': 'No recent duplicates found',
            'date': 'None within 5 minutes'
        }

        debt_info = {
            'status': 'Validation passed',
            'balance_text': subscriber_info.get('balance_text', 'N/A'),
            'has_debt': subscriber_info.get('has_debt', False),
            'debt_amount': subscriber_info.get('debt_amount', 0),
            'validation_result': 'Payment allowed',
            'validation_passed': True,
            'validation_message': 'Payment allowed'
        }

        # إعداد معلومات الحزمة للعرض
        package_info = {
            'package_price': package_price_value,
            'is_sufficient': int(amount) >= int(package_price_value),
            'message': f'Package price: {package_price_value} SYP'
        }

        # Display detailed payment summary with all collected information
        user_confirmed = display_payment_summary_inet(order, amount, duplicate_info, debt_info, subscriber_info, package_info)

        if not user_confirmed:
            log(f"inet payment skipped by user after detailed review for order {order_id}", "WARNING")
            # Order processing skipped by user
            return False

        # Now proceed with actual payment process with user confirmations
        log("Starting inet payment process with user confirmations for each step", "INFO")

        try:
            # Step 5: Get payment form data
            print(f"\n🔄 STEP 5/7: GET PAYMENT FORM DATA")
            print("="*50)
            print(f"📋 Getting payment form for subscriber ID: {sub_id}")

            # Ask user confirmation before getting payment form
            # Automatically proceed (no user confirmation needed)
            print("🤖 Automatically proceeding with payment form data...")
            log("Step 5: Getting payment form data", "INFO")
            form_data = get_payment_form_data_inet(session, sub_id, pg, papp)
            if not form_data:
                log("Failed to get payment form data", "ERROR")
                log_payment_details(order_id, number, amount, "3", "Failed to get payment form data")
                display_payment_result_inet(order_id, 3, "خطأ في معالجة الطلب: فشل في الحصول على بيانات نموذج الدفع")
                return False

            # Add sub_id to form_data for later use
            form_data['sub_id'] = sub_id
            print(f"✅ Step 5 completed successfully - Payment form data obtained!")

            # Step 6: First payment confirmation
            print(f"\n🔄 STEP 6/7: FIRST PAYMENT CONFIRMATION")
            print("="*50)
            print(f"📋 Preparing first payment confirmation for amount: {amount} SYP")

            # Ask user confirmation before first payment confirmation
            # Automatically proceed (no user confirmation needed)
            print("🤖 Automatically proceeding...")
            log("Step 6: Performing first payment confirmation", "INFO")
            first_confirmation = perform_first_payment_confirmation_inet(session, form_data, amount, pg, papp)
            if not first_confirmation:
                log("First confirmation failed", "ERROR")
                log_payment_details(order_id, number, amount, "3", "First confirmation failed")
                display_payment_result_inet(order_id, 3, "خطأ في معالجة الطلب: فشل في التأكيد الأول")
                return False
            print(f"✅ Step 6 completed successfully - First confirmation done!")

            # Step 7: Final payment confirmation and balance verification
            print(f"\n🔄 STEP 7/7: FINAL PAYMENT CONFIRMATION")
            print("="*50)
            print(f"⚠️ THIS IS THE FINAL STEP - PAYMENT WILL BE COMPLETED!")
            print(f"📋 Final payment confirmation for amount: {amount} SYP")
            print(f"📱 Phone number: {number}")
            print(f"👤 Subscriber: {subscriber_info.get('name', 'N/A')}")

            # Ask user confirmation before final payment confirmation
            # Automatically proceed (no user confirmation needed)
            print("🤖 Automatically proceeding...")
            log("Step 7: Performing final payment confirmation", "INFO")
            final_confirmation, _ = perform_final_payment_confirmation_inet(session, form_data, amount, pg, papp)
            if not final_confirmation:
                log("Final confirmation failed", "ERROR")
                log_payment_details(order_id, number, amount, "3", "Final confirmation failed")
                display_payment_result_inet(order_id, 3, "فشل في عملية الدفع حتى بعد المحاولة الثانية")
                return False
            print(f"✅ Step 7 completed successfully - Final confirmation done!")

            # Step 8: Check gateway balance after payment to verify success (like Sawa method)
            log("Step 8: Checking gateway balance after payment to verify success...", "INFO")
            gateway_balance_after = check_gateway_balance_inet(session)
            if gateway_balance_after == -1:
                log("Failed to extract gateway balance after payment", "WARNING")
            else:
                log(f"Gateway balance after payment: {gateway_balance_after}", "INFO")

            # Determine payment success by comparing gateway balances (like Sawa)
            # Only if both balance extractions were successful (not -1)
            if gateway_balance_before == -1 or gateway_balance_after == -1:
                log("Cannot verify payment success - failed to extract gateway balance", "WARNING")
                payment_successful = False  # Consider failed if we can't verify
                error_message = "خطأ في معالجة الطلب: فشل في التحقق من تغيير الرصيد"
            else:
                payment_successful = gateway_balance_before != gateway_balance_after

                if payment_successful:
                    balance_change = gateway_balance_before - gateway_balance_after
                    log(f"Payment successful - gateway balance changed from {gateway_balance_before} to {gateway_balance_after} (decrease: {balance_change})", "SUCCESS")
                else:
                    log(f"Payment failed - gateway balance unchanged: {gateway_balance_before}", "ERROR")
                    error_message = "فشل الدفع - لم يتغير الرصيد حتى بعد المحاولة الثانية"

            log("All inet payment steps completed", "INFO")

        except Exception as e:
            log(f"Error during inet payment process: {e}", "ERROR")
            log_payment_details(order_id, number, amount, "3", f"Payment process error: {str(e)}")
            display_payment_result_inet(order_id, 3, f"خطأ في معالجة الطلب: {str(e)}")
            return False

        if payment_successful:
            log(f"inet payment successful for order {order_id}", "SUCCESS")
            log_payment_details(order_id, number, amount, "1", f"Gateway balance changed from {gateway_balance_before} to {gateway_balance_after}")
            # Display success result
            display_payment_result_inet(order_id, 1)
        else:
            log(f"inet payment failed for order {order_id} - gateway balance unchanged", "ERROR")
            log_payment_details(order_id, number, amount, "3", f"Gateway balance unchanged: {gateway_balance_before}")
            display_payment_result_inet(order_id, 3, error_message)

        return payment_successful

    except Exception as e:
        log(f"Error processing inet payment for order {order_id}: {e}", "ERROR")
        log_payment_details(order_id, number, amount, "3", f"Processing error: {str(e)}")

        # Display error result
        display_payment_result_inet(order_id, 3, f"خطأ في معالجة الطلب: {str(e)}")
        return False

def process_single_order_from_input_inet():
    """
    Process a single inet order from user input instead of fetching from API
    """
    try:
        # License verification removed - running in standalone mode

        print("=== معالج دفع INET - طلب واحد ===")
        print("يرجى إدخال بيانات الطلب:")

        # Get order details from user input
        order_id = input("رقم الطلب (Order ID): ").strip()
        phone_number = input("رقم الهاتف: ").strip()
        amount = input("المبلغ: ").strip()

        if not order_id or not phone_number or not amount:
            print("❌ يجب إدخال جميع البيانات المطلوبة")
            return False

        try:
            amount = float(amount)
        except ValueError:
            print("❌ المبلغ يجب أن يكون رقم صحيح")
            return False

        # Create order object similar to API format
        order = {
            "id": order_id,
            "phone_number": phone_number,
            "amount": amount,
            "product": 47  # INET product ID
        }

        print(f"\n📋 معلومات الطلب:")
        print(f"رقم الطلب: {order_id}")
        print(f"رقم الهاتف: {phone_number}")
        print(f"المبلغ: {amount}")
        print("\n🔄 بدء معالجة الطلب...")

        # Process the single order
        success = process_single_payment_inet(order)

        if success:
            print(f"\n✅ تم معالجة الطلب {order_id} بنجاح")
            log(f"Order {order_id} processed successfully", "SUCCESS")
            return True
        else:
            print(f"\n❌ فشل في معالجة الطلب {order_id}")
            log(f"Order {order_id} processing failed", "ERROR")
            return False

    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف العملية بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n❌ خطأ في معالجة الطلب: {e}")
        log(f"Error in process_single_order_from_input_inet: {e}", "ERROR")
        return False


if __name__ == "__main__":
    try:
        # Start the single order inet payment processor
        process_single_order_from_input_inet()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye from inet Payment Processor!")
    except Exception as e:
        print(f"❌ Fatal inet error: {e}")
        log(f"Fatal inet error: {e}", "ERROR")

def process_single_payment_inet_api(order):
    """
    API wrapper for INET payment processing that returns detailed status information

    Args:
        order: Order object with id, phone_number, amount, type

    Returns:
        tuple: (success, status, message)
    """
    order_type = order.get("type", "")
    original_number = order["phone_number"]
    number = format_phone_number_inet(original_number)  # Format phone number with '41' prefix
    order_id = str(order.get("id"))
    amount = order["amount"]

    log(f"Processing inet order ID: {order_id}, Original Phone: {original_number}, Formatted Phone: {number}, Amount: {amount}", "INFO")

    try:
        # Step 1: Login to inet system
        log("Step 1: Logging into inet system...", "INFO")
        success, session, pg, papp, login_balance = perform_inet_login()
        if not success:
            log(f"inet login failed for order {order_id}", "ERROR")
            log_payment_details(order_id, number, amount, "3", "inet login failed")
            return False, 3, "خطأ في معالجة الطلب: فشل في تسجيل الدخول إلى نظام inet"

        # Use balance from login if available
        if login_balance is not None:
            gateway_balance_before = login_balance
            log(f"Using gateway balance from login response: {gateway_balance_before}", "INFO")
        else:
            # Step 2: Check gateway balance before payment (always check regardless of session source)
            log("Step 2: Checking inet gateway balance before payment...", "INFO")
            gateway_balance_before = check_gateway_balance_inet(session)

            if gateway_balance_before == -1:
                log("Failed to extract gateway balance before payment", "WARNING")
                gateway_balance_before = None
            else:
                log(f"Gateway balance before payment: {gateway_balance_before}", "INFO")

        # Step 3: Search for phone number
        log(f"Step 3: Searching for phone number {number} in inet system...", "INFO")
        subscriber_info, sub_id = search_phone_number_inet(session, number, pg, papp)

        # Handle session expiry
        if subscriber_info == "SESSION_EXPIRED":
            log(f"Session expired during search for order {order_id}, attempting re-login", "WARNING")
            success, session, pg, papp, login_balance = perform_inet_login()
            if success:
                log("Re-login successful, retrying search", "INFO")
                subscriber_info, sub_id = search_phone_number_inet(session, number, pg, papp)
                if subscriber_info == "SESSION_EXPIRED":
                    log(f"Session expired again after re-login for order {order_id}", "ERROR")
                    log_payment_details(order_id, number, amount, "3", "Persistent session expiry issue")
                    return False, 3, "خطأ في معالجة الطلب: مشكلة مستمرة في انتهاء صلاحية الجلسة"
            else:
                log(f"Re-login failed for order {order_id}", "ERROR")
                log_payment_details(order_id, number, amount, "3", "Session expired and re-login failed")
                return False, 3, "خطأ في معالجة الطلب: انتهت صلاحية الجلسة وفشل إعادة تسجيل الدخول"
        elif subscriber_info is None and sub_id is None:
            log("Search failed - no subscriber found", "WARNING")

        # فحص الخطأ التقني (رسالة الانتظار 5 دقائق)
        if subscriber_info == "TECHNICAL_ERROR_5MIN_WAIT":
            log(f"Technical error: 5-minute wait message detected for phone {number}", "ERROR")
            log_payment_details(order_id, number, amount, "3", "Technical error: 5-minute wait between payments")
            return False, 3, "خطأ تقني: الرجاء الانتظار 5 دقائق بين الدفعات المتتالية"

        # التحقق من وجود الرقم والكلفة الشهرية - إذا لم نجد معلومات المشترك أو لم نجد الكلفة الشهرية
        package_price_value = -1
        if subscriber_info and sub_id:
            package_price_value = extract_package_price_inet(subscriber_info)

        # إذا لم نجد معلومات المشترك أو لم نجد الكلفة الشهرية = خطأ في الرقم أو الشركة
        if not subscriber_info or not sub_id or package_price_value <= 0:
            log(f"Phone number {number} not found or invalid - wrong number or company", "ERROR")
            log_payment_details(order_id, number, amount, "2", "Phone number not found or invalid - wrong number or company")
            return False, 2, "خطأ الرقم أو الشركة"

        # التحقق من الدين أولاً - إذا كان هناك دين ولا يطابق المبلغ المطلوب، يرسل "المبلغ المطلوب" + الرقم
        log("Step 4: Checking subscriber debt status...", "INFO")
        if subscriber_info.get('has_debt', False):
            debt_amount = subscriber_info.get('debt_amount', 0)
            is_debt_valid, debt_message = validate_debt_payment_inet(debt_amount, amount)

            if not is_debt_valid:
                log(f"Payment rejected due to debt: {debt_message}", "ERROR")
                # إرسال "المبلغ المطلوب" متبوعاً بمبلغ الدين
                debt_message = f"المبلغ المطلوب {int(debt_amount)}"
                log_payment_details(order_id, number, amount, "4", f"Subscriber has debt: {debt_amount}")
                return False, 4, debt_message

        # التحقق من مطابقة سعر الحزمة للطلبات من نوع MB
        if "MB" in order_type:
            log("Checking package price for MB order", "INFO")

            # التحقق من كفاية المبلغ لسعر الحزمة - إذا كان المبلغ أقل من سعر الحزمة، يرسل "المبلغ المطلوب" + الرقم
            if int(amount) < int(package_price_value):
                log(f"Payment amount ({amount}) is less than package price ({package_price_value})", "WARNING")
                # إرسال "المبلغ المطلوب" متبوعاً بكلفة الحزمة الشهرية
                package_message = f"المبلغ المطلوب {int(package_price_value)}"
                log_payment_details(order_id, number, amount, "4", f"Amount less than package price: {amount} < {package_price_value}")
                return False, 4, package_message

            # إذا كان المبلغ يطابق سعر الحزمة، يجب التحقق من الدين مرة أخرى
            log(f"Payment amount matches package price - proceeding with debt verification", "INFO")

        # Continue with payment processing...
        # Step 5: Get payment form data
        log("Step 5: Getting payment form data...", "INFO")
        payment_form_data = get_payment_form_data_inet(session, sub_id, pg, papp)

        if not payment_form_data:
            log("Failed to get payment form data", "ERROR")
            log_payment_details(order_id, number, amount, "3", "Failed to get payment form data")
            return False, 3, "خطأ في معالجة الطلب: فشل في الحصول على بيانات نموذج الدفع"

        # Step 6: First payment confirmation
        log("Step 6: Performing first payment confirmation", "INFO")
        first_confirmation = perform_first_payment_confirmation_inet(session, payment_form_data, amount, pg, papp)
        if not first_confirmation:
            log("First confirmation failed", "ERROR")
            log_payment_details(order_id, number, amount, "3", "First confirmation failed")
            return False, 3, "خطأ في معالجة الطلب: فشل في التأكيد الأول"

        # Step 7: Final payment confirmation and balance verification
        log("Step 7: Performing final payment confirmation", "INFO")
        payment_successful = perform_final_payment_confirmation_inet(session, payment_form_data, amount, pg, papp)

        # Step 8: Check gateway balance after payment
        log("Step 8: Checking gateway balance after payment...", "INFO")
        gateway_balance_after = check_gateway_balance_inet(session)

        if gateway_balance_after == -1:
            log("Failed to extract gateway balance after payment", "WARNING")
            gateway_balance_after = None
        else:
            log(f"Gateway balance after payment: {gateway_balance_after}", "INFO")

        # Determine payment success based on balance change
        error_message = "فشل في عملية الدفع"
        if gateway_balance_before is not None and gateway_balance_after is not None:
            try:
                balance_before_float = float(gateway_balance_before)
                balance_after_float = float(gateway_balance_after)

                # Payment is successful if balance decreased
                if balance_before_float > balance_after_float:
                    payment_successful = True
                    balance_change = balance_before_float - balance_after_float
                    log(f"Payment successful - gateway balance changed from {balance_before_float} to {balance_after_float} (decrease: {balance_change})", "SUCCESS")
                else:
                    payment_successful = False
                    log(f"Payment failed - gateway balance unchanged: {balance_before_float}", "ERROR")
                    error_message = "فشل الدفع - لم يتغير الرصيد"
            except (ValueError, TypeError) as e:
                log(f"Error converting balances to float: {e}", "ERROR")
                payment_successful = False
                error_message = "خطأ في معالجة الطلب: خطأ في تحويل قيم الرصيد"

        log("All inet payment steps completed", "INFO")

        if payment_successful:
            log(f"inet payment successful for order {order_id}", "SUCCESS")
            log_payment_details(order_id, number, amount, "1", f"Gateway balance changed from {gateway_balance_before} to {gateway_balance_after}")
            return True, 1, "تم الدفع بنجاح"
        else:
            log(f"inet payment failed for order {order_id} - gateway balance unchanged", "ERROR")
            log_payment_details(order_id, number, amount, "3", f"Gateway balance unchanged: {gateway_balance_before}")
            return False, 3, error_message

    except Exception as e:
        log(f"Error processing inet payment for order {order_id}: {e}", "ERROR")
        log_payment_details(order_id, number, amount, "3", f"Processing error: {str(e)}")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"
