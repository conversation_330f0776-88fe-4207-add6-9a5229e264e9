{"actorSpecification": 1, "name": "tawasowl-payment-api", "title": "TawasOwl Payment API Actor", "description": "Automated payment processing for Syrian telecom and internet service providers including Sawa, Syriatel, INET, MTS, LEMA, Linet, Bitakat, and Takamol. Processes payment requests and returns standardized responses.", "version": "1.0.0", "meta": {"templateId": "node-puppeteer"}, "input": "./input_schema.json", "dockerfile": "./Dockerfile", "readme": "./README.md", "categories": ["AUTOMATION", "BUSINESS"], "isPublic": false, "seoTitle": "Syrian Telecom Payment Processing Actor", "seoDescription": "Automate payments for Syrian telecom providers: Sawa, Syriatel, INET, MTS, LEMA, Linet, Bitakat, Takamol"}