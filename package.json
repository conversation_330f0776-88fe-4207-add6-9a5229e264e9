{"name": "tawasowl-payment-api-actor", "version": "1.0.0", "description": "Apify Actor for automated payment processing across multiple Syrian telecom and internet service providers", "main": "main.js", "scripts": {"start": "node main.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["apify", "actor", "payment", "automation", "syria", "telecom", "sawa", "syriatel", "inet", "mts", "lema", "linet", "<PERSON><PERSON><PERSON>", "takamol"], "author": "TawasOwl Payment System", "license": "MIT", "dependencies": {"apify": "^3.1.10", "puppeteer": "^21.5.2", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "fake-useragent": "^1.0.1", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"eslint": "^8.55.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/tawasowl-payment-api-actor"}, "apify": {"actorSpecification": 1, "name": "tawasowl-payment-api", "title": "TawasOwl Payment API Actor", "description": "Automated payment processing for Syrian telecom and internet service providers including Sawa, Syriatel, INET, MTS, LEMA, Linet, Bitakat, and Takamol", "version": "1.0.0", "meta": {"templateId": "node-puppeteer"}, "input": "./input_schema.json", "dockerfile": "./Dockerfile"}}