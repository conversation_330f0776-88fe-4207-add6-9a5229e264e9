const { log } = require('apify').utils;
const configManager = require('./config-manager');

// Import individual payment processors
const SawaProcessor = require('./processors/sawa-processor');
const SyriatelProcessor = require('./processors/syriatel-processor');
const InetProcessor = require('./processors/inet-processor');
const MtsProcessor = require('./processors/mts-processor');
const LemaProcessor = require('./processors/lema-processor');
const LinetProcessor = require('./processors/linet-processor');
const BitakatProcessor = require('./processors/bitakat-processor');
const TakamolProcessor = require('./processors/takamol-processor');

/**
 * Payment Processors Manager
 * Handles routing payment requests to appropriate processors
 */
class PaymentProcessors {
    constructor() {
        this.processors = new Map();
        this.initialized = false;
    }

    /**
     * Initialize all payment processors
     */
    async initialize() {
        try {
            log.info('🔄 Initializing payment processors...');

            // Initialize processors mapping
            this.processors.set(25, SyriatelProcessor);  // Syriatel
            this.processors.set(29, SawaProcessor);      // Sawa
            this.processors.set(46, LinetProcessor);     // Linet
            this.processors.set(47, InetProcessor);      // INET
            this.processors.set(48, MtsProcessor);       // MTS
            this.processors.set(49, LemaProcessor);      // LEMA
            this.processors.set(50, BitakatProcessor);   // Bitakat
            this.processors.set(51, TakamolProcessor);   // Takamol

            // Initialize each processor
            for (const [productId, processor] of this.processors) {
                try {
                    if (processor.initialize) {
                        await processor.initialize();
                    }
                    log.info(`✅ Processor initialized for product ID ${productId}`);
                } catch (error) {
                    log.warning(`⚠️ Failed to initialize processor for product ID ${productId}: ${error.message}`);
                }
            }

            this.initialized = true;
            log.info('✅ All payment processors initialized successfully');
            return true;

        } catch (error) {
            log.error('❌ Failed to initialize payment processors', { error: error.message });
            throw error;
        }
    }

    /**
     * Process a payment request
     */
    async processPayment(productId, order) {
        if (!this.initialized) {
            throw new Error('Payment processors not initialized');
        }

        const processor = this.processors.get(productId);
        if (!processor) {
            throw new Error(`No processor found for product ID: ${productId}`);
        }

        try {
            log.info(`🔄 Processing payment with processor for product ID ${productId}`, {
                orderId: order.id,
                amount: order.amount,
                phoneNumber: order.phone_number
            });

            const result = await processor.processPayment(order);
            
            log.info(`✅ Payment processed successfully for product ID ${productId}`, {
                orderId: order.id,
                status: result.status,
                message: result.message
            });

            return result;

        } catch (error) {
            log.error(`❌ Payment processing failed for product ID ${productId}`, {
                orderId: order.id,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get processor for a specific product ID
     */
    getProcessor(productId) {
        return this.processors.get(productId);
    }

    /**
     * Get all supported product IDs
     */
    getSupportedProducts() {
        return Array.from(this.processors.keys());
    }

    /**
     * Check if a product ID is supported
     */
    isProductSupported(productId) {
        return this.processors.has(productId);
    }

    /**
     * Get processor status for all products
     */
    async getProcessorStatus() {
        const status = {};
        
        for (const [productId, processor] of this.processors) {
            try {
                const companyInfo = configManager.getCompanyByProductId(productId);
                const companyName = companyInfo?.config?.name || `Product ${productId}`;
                
                status[productId] = {
                    name: companyName,
                    available: true,
                    lastCheck: new Date().toISOString()
                };

                // Check if processor has a health check method
                if (processor.healthCheck) {
                    const healthResult = await processor.healthCheck();
                    status[productId].healthy = healthResult.healthy;
                    status[productId].details = healthResult.details;
                }

            } catch (error) {
                status[productId] = {
                    name: `Product ${productId}`,
                    available: false,
                    error: error.message,
                    lastCheck: new Date().toISOString()
                };
            }
        }

        return status;
    }

    /**
     * Validate order data
     */
    validateOrder(order) {
        const requiredFields = ['id', 'phone_number', 'amount', 'product'];
        const errors = [];

        for (const field of requiredFields) {
            if (!(field in order) || order[field] === null || order[field] === undefined) {
                errors.push(`Missing required field: ${field}`);
            }
        }

        // Validate amount
        if (order.amount !== undefined) {
            const amount = parseFloat(order.amount);
            if (isNaN(amount) || amount <= 0) {
                errors.push('Amount must be a positive number');
            }
        }

        // Validate phone number
        if (order.phone_number !== undefined && !String(order.phone_number).trim()) {
            errors.push('Phone number cannot be empty');
        }

        // Validate product ID
        if (order.product !== undefined && !this.isProductSupported(order.product)) {
            errors.push(`Unsupported product ID: ${order.product}`);
        }

        if (errors.length > 0) {
            throw new Error(`Order validation failed: ${errors.join(', ')}`);
        }

        return true;
    }

    /**
     * Process payment with validation and error handling
     */
    async processPaymentSafe(productId, order) {
        try {
            // Validate order
            this.validateOrder({ ...order, product: productId });

            // Get company configuration
            const companyInfo = configManager.getCompanyByProductId(productId);
            if (!companyInfo) {
                throw new Error(`No configuration found for product ID: ${productId}`);
            }

            // Check payment limits
            const limits = configManager.getPaymentLimits(companyInfo.key);
            if (limits) {
                const amount = parseFloat(order.amount);
                if (amount < limits.minAmount) {
                    return {
                        success: false,
                        status: 3,
                        message: `المبلغ أقل من الحد الأدنى المسموح (${limits.minAmount})`
                    };
                }
                if (amount > limits.maxAmount) {
                    return {
                        success: false,
                        status: 3,
                        message: `المبلغ أكبر من الحد الأقصى المسموح (${limits.maxAmount})`
                    };
                }
            }

            // Process payment
            const result = await this.processPayment(productId, order);
            return {
                success: result.status === 1,
                status: result.status,
                message: result.message
            };

        } catch (error) {
            log.error(`❌ Safe payment processing failed for product ID ${productId}`, {
                orderId: order.id,
                error: error.message
            });

            return {
                success: false,
                status: 3,
                message: `خطأ في معالجة الطلب: ${error.message}`
            };
        }
    }
}

// Create singleton instance
const paymentProcessors = new PaymentProcessors();

module.exports = paymentProcessors;
