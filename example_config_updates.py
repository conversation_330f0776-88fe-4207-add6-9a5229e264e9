#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أمثلة على تحديث إعدادات الشركات
===============================

هذا الملف يحتوي على أمثلة عملية لكيفية تحديث إعدادات الشركات برمجياً.
"""

from config_manager import ConfigManager

def example_update_sawa_password():
    """مثال: تحديث كلمة سر شركة سوا"""
    print("🔐 مثال: تحديث كلمة سر شركة سوا")
    print("-" * 40)
    
    config = ConfigManager()
    
    # عرض كلمة السر الحالية (مخفية)
    current_config = config.get_company_config('sawa')
    if current_config:
        current_password = current_config['login']['password']
        print(f"كلمة السر الحالية: {'*' * len(current_password)}")
    
    # تحديث كلمة السر
    new_password = "new_password_123"
    success = config.update_company_credentials('sawa', password=new_password)
    
    if success:
        print(f"✅ تم تحديث كلمة سر سوا بنجاح")
        
        # إعادة كلمة السر الأصلية للمثال
        config.update_company_credentials('sawa', password=current_password)
        print("🔄 تم إرجاع كلمة السر الأصلية")
    else:
        print("❌ فشل في تحديث كلمة السر")

def example_update_bitakat_limits():
    """مثال: تحديث حدود الدفع لشركة بطاقات"""
    print("\n💰 مثال: تحديث حدود الدفع لشركة بطاقات")
    print("-" * 40)
    
    config = ConfigManager()
    
    # عرض الحدود الحالية
    current_config = config.get_company_config('bitakat')
    if current_config:
        current_max = current_config['payment']['max_amount']
        current_min = current_config['payment']['min_amount']
        print(f"الحد الأقصى الحالي: {current_max:,}")
        print(f"الحد الأدنى الحالي: {current_min:,}")
    
    # تحديث الحدود
    new_max = 150000
    new_min = 10
    success = config.update_payment_limits('bitakat', max_amount=new_max, min_amount=new_min)
    
    if success:
        print(f"✅ تم تحديث حدود بطاقات - الأقصى: {new_max:,}, الأدنى: {new_min}")
        
        # إعادة الحدود الأصلية للمثال
        config.update_payment_limits('bitakat', max_amount=current_max, min_amount=current_min)
        print("🔄 تم إرجاع الحدود الأصلية")
    else:
        print("❌ فشل في تحديث الحدود")

def example_get_company_by_product_id():
    """مثال: البحث عن شركة بـ Product ID"""
    print("\n🔍 مثال: البحث عن شركة بـ Product ID")
    print("-" * 40)
    
    config = ConfigManager()
    
    # البحث عن شركة بـ Product ID
    product_id = 46  # Linet
    company_key, company_config = config.get_company_by_product_id(product_id)
    
    if company_config:
        print(f"Product ID {product_id} ينتمي إلى:")
        print(f"  الشركة: {company_config['name']} ({company_key})")
        print(f"  اسم المستخدم: {company_config['login']['username']}")
        print(f"  الحد الأقصى: {company_config['payment']['max_amount']:,}")
    else:
        print(f"❌ لم يتم العثور على شركة بـ Product ID {product_id}")

def example_show_all_credentials():
    """مثال: عرض جميع أسماء المستخدمين (كلمات السر مخفية)"""
    print("\n👥 مثال: عرض جميع أسماء المستخدمين")
    print("-" * 40)
    
    config = ConfigManager()
    companies = config.list_all_companies()
    
    for company in companies:
        credentials = config.get_login_credentials(company['key'])
        print(f"🏢 {company['name']}:")
        print(f"   اسم المستخدم: {credentials.get('username', 'غير محدد')}")
        print(f"   كلمة السر: {'*' * len(credentials.get('password', ''))}")
        print(f"   رابط الدخول: {credentials.get('url', 'غير محدد')}")
        print()

def example_validate_config():
    """مثال: التحقق من صحة الإعدادات"""
    print("\n✅ مثال: التحقق من صحة الإعدادات")
    print("-" * 40)
    
    config = ConfigManager()
    
    # التحقق من وجود جميع الشركات المطلوبة
    required_companies = ['sawa', 'syriatel', 'bitakat', 'linet', 'lema', 'mts', 'inet', 'takamol']
    missing_companies = []
    
    for company_key in required_companies:
        company_config = config.get_company_config(company_key)
        if not company_config:
            missing_companies.append(company_key)
        else:
            # التحقق من وجود الحقول المطلوبة
            required_fields = ['product_id', 'name', 'login']
            missing_fields = []
            
            for field in required_fields:
                if field not in company_config:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"⚠️ {company_config.get('name', company_key)}: حقول مفقودة - {missing_fields}")
            else:
                print(f"✅ {company_config['name']}: جميع الحقول موجودة")
    
    if missing_companies:
        print(f"\n❌ شركات مفقودة: {missing_companies}")
    else:
        print(f"\n✅ جميع الشركات المطلوبة موجودة ({len(required_companies)} شركة)")

def example_backup_config():
    """مثال: إنشاء نسخة احتياطية من الإعدادات"""
    print("\n💾 مثال: إنشاء نسخة احتياطية")
    print("-" * 40)
    
    import shutil
    from datetime import datetime
    
    try:
        # إنشاء اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"companies_config_backup_{timestamp}.json"
        
        # نسخ الملف
        shutil.copy2("companies_config.json", backup_filename)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_filename}")
        
        return backup_filename
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
        return None

if __name__ == "__main__":
    print("🧪 أمثلة على تحديث إعدادات الشركات")
    print("=" * 50)
    
    # تشغيل جميع الأمثلة
    example_update_sawa_password()
    example_update_bitakat_limits()
    example_get_company_by_product_id()
    example_show_all_credentials()
    example_validate_config()
    
    # إنشاء نسخة احتياطية
    backup_file = example_backup_config()
    
    print("\n" + "=" * 50)
    print("🏁 انتهت الأمثلة!")
    print("\nيمكنك الآن:")
    print("1. تشغيل python update_config.py للأداة التفاعلية")
    print("2. تعديل companies_config.json مباشرة")
    print("3. استخدام config_manager في الكود الخاص بك")
    
    if backup_file:
        print(f"\n💾 تم إنشاء نسخة احتياطية: {backup_file}")
