const puppeteer = require('puppeteer');
const { log } = require('apify').utils;
const configManager = require('../config-manager');
const BaseProcessor = require('./base-processor');

/**
 * Syriatel Payment Processor
 * Handles automated payment processing for Syriatel telecom
 */
class SyriatelProcessor extends BaseProcessor {
    constructor() {
        super('syriatel');
        this.companyName = 'سيرياتيل';
        this.productId = 25;
    }

    /**
     * Process a single payment using Puppeteer
     */
    async processPayment(order) {
        let browser = null;
        let page = null;

        try {
            log.info(`🔄 Starting Syriatel payment processing for order ${order.id}`);

            // Validate order
            this.validateOrder(order);

            // Check payment limits
            const limitCheck = this.checkPaymentLimits(order.amount);
            if (!limitCheck.valid) {
                return this.createErrorResponse(3, limitCheck.error);
            }

            // Get configuration
            const credentials = configManager.getLoginCredentials(this.companyKey);
            if (!credentials) {
                throw new Error('Syriatel credentials not found in configuration');
            }

            // Setup browser
            const browserSetup = await this.setupBrowser(puppeteer);
            browser = browserSetup.browser;
            page = browserSetup.page;

            // Navigate to login page
            log.info(`Navigating to Syriatel login page: ${credentials.url}`);
            await page.goto(credentials.url, { waitUntil: 'networkidle2', timeout: 30000 });
            
            // Wait for page to load
            await page.waitForFunction(() => document.readyState === 'complete');
            await this.randomSleep(1000, 2000);

            // Enter credentials
            log.info(`Entering username: ${credentials.username}`);
            await this.typeHumanLike(page, 'input[name="username"], #username', credentials.username);
            await this.randomSleep(500, 1000);

            log.info('Entering password');
            await this.typeHumanLike(page, 'input[name="password"], #password', credentials.password);
            await this.randomSleep(500, 1000);

            // Click login button
            log.info('Clicking login button');
            await page.click('input[type="submit"], button[type="submit"], .login-btn');
            
            // Wait for login to complete
            await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 });
            log.info('✅ Login successful');
            await this.randomSleep(1000, 2000);

            // Navigate to payment page
            const companyConfig = configManager.getCompanyConfig(this.companyKey);
            const paymentUrl = companyConfig.payment?.bulk?.url || companyConfig.payment?.cash?.url;
            
            if (paymentUrl) {
                log.info(`Navigating to payment page: ${paymentUrl}`);
                await page.goto(paymentUrl, { waitUntil: 'networkidle2' });
                await this.randomSleep(1000, 2000);
            }

            // Format phone number
            const formattedPhone = this.formatPhoneNumber(order.phone_number);
            
            // Enter phone number
            log.info(`🔍 Entering phone number: ${formattedPhone}`);
            await this.typeHumanLike(page, 'input[name="phone"], input[name="number"], #phone', formattedPhone);
            await this.randomSleep(500, 1000);

            // Enter payment amount
            log.info(`💰 Entering payment amount: ${order.amount}`);
            await this.typeHumanLike(page, 'input[name="amount"], #amount', String(order.amount));
            await this.randomSleep(500, 1000);

            // Submit payment
            log.info('💳 Submitting payment');
            await page.click('input[type="submit"], button[type="submit"], .submit-btn, .pay-btn');
            
            // Wait for result
            await this.randomSleep(3000, 5000);
            await page.waitForFunction(() => document.readyState === 'complete');

            // Check for errors first
            const errorMessage = await this.checkForErrors(page);
            if (errorMessage) {
                log.warning(`❌ Payment failed for order ${order.id}: ${errorMessage}`);
                
                // Check for specific error types
                if (errorMessage.includes('غير موجود') || errorMessage.includes('not found')) {
                    return this.createErrorResponse(2, "الرقم غير موجود في النظام");
                } else if (errorMessage.includes('دين') || errorMessage.includes('debt')) {
                    return this.createErrorResponse(4, "المشترك عليه دين");
                } else {
                    return this.createErrorResponse(3, errorMessage);
                }
            }

            // Check for success
            const successMessage = await this.checkForSuccess(page);
            if (successMessage || 
                successMessage?.includes('نجح') || 
                successMessage?.includes('تم') ||
                successMessage?.includes('success')) {
                
                log.info(`✅ Payment successful for order ${order.id}`);
                this.logPaymentDetails(order.id, order.phone_number, order.amount, 1, 'Success');
                return this.createSuccessResponse();
            }

            // If no clear success/error message, check page content
            const pageContent = await page.content();
            if (pageContent.includes('نجح') || pageContent.includes('تم') || pageContent.includes('success')) {
                log.info(`✅ Payment successful for order ${order.id} (detected from page content)`);
                this.logPaymentDetails(order.id, order.phone_number, order.amount, 1, 'Success detected from content');
                return this.createSuccessResponse();
            }

            // Default to failure if no clear indication
            log.warning(`❌ Payment result unclear for order ${order.id}`);
            this.logPaymentDetails(order.id, order.phone_number, order.amount, 3, 'Unclear result');
            return this.createErrorResponse(3, "نتيجة الدفع غير واضحة");

        } catch (error) {
            log.error(`❌ Syriatel payment processing failed for order ${order.id}: ${error.message}`);
            this.logPaymentDetails(order.id, order.phone_number, order.amount, 3, error.message);
            return this.createErrorResponse(3, `خطأ في معالجة الطلب: ${error.message}`);

        } finally {
            await this.cleanupBrowser(browser, page);
        }
    }

    /**
     * Health check for Syriatel processor
     */
    async healthCheck() {
        try {
            const credentials = configManager.getLoginCredentials(this.companyKey);
            const companyConfig = configManager.getCompanyConfig(this.companyKey);
            
            if (!credentials) {
                return { healthy: false, details: 'Credentials missing' };
            }

            if (!companyConfig) {
                return { healthy: false, details: 'Configuration missing' };
            }

            return { 
                healthy: true, 
                details: 'Configuration loaded',
                paymentMethods: {
                    bulk: !!companyConfig.payment?.bulk,
                    cash: !!companyConfig.payment?.cash
                }
            };

        } catch (error) {
            return { healthy: false, details: error.message };
        }
    }
}

module.exports = new SyriatelProcessor();
