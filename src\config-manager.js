const fs = require('fs').promises;
const path = require('path');
const { log } = require('apify').utils;

/**
 * Configuration Manager for Payment Companies
 * Handles loading and managing configuration for all payment companies
 */
class ConfigManager {
    constructor(configFile = 'companies_config.json') {
        this.configFile = configFile;
        this.config = null;
    }

    /**
     * Initialize and load configuration
     */
    async initialize() {
        try {
            await this.loadConfig();
            log.info('✅ Configuration manager initialized successfully');
            return true;
        } catch (error) {
            log.error('❌ Failed to initialize configuration manager', { error: error.message });
            throw error;
        }
    }

    /**
     * Load configuration from JSON file
     */
    async loadConfig() {
        try {
            const configPath = path.resolve(this.configFile);
            const configData = await fs.readFile(configPath, 'utf8');
            this.config = JSON.parse(configData);
            
            log.info(`✅ Configuration loaded successfully from ${this.configFile}`);
            return true;
        } catch (error) {
            if (error.code === 'ENOENT') {
                log.error(`❌ Configuration file ${this.configFile} not found`);
            } else {
                log.error(`❌ Error loading configuration: ${error.message}`);
            }
            throw error;
        }
    }

    /**
     * Get configuration for a specific company
     */
    getCompanyConfig(companyKey) {
        if (!this.config || !this.config.companies) {
            log.warning('Configuration not loaded or companies section missing');
            return null;
        }

        const company = this.config.companies[companyKey];
        if (!company) {
            log.warning(`Company configuration not found: ${companyKey}`);
            return null;
        }

        return company;
    }

    /**
     * Get company configuration by product ID
     */
    getCompanyByProductId(productId) {
        if (!this.config || !this.config.companies) {
            return null;
        }

        for (const [companyKey, companyConfig] of Object.entries(this.config.companies)) {
            if (companyConfig.product_id === productId) {
                return { key: companyKey, config: companyConfig };
            }
        }

        log.warning(`No company found for product ID: ${productId}`);
        return null;
    }

    /**
     * Get login credentials for a company
     */
    getLoginCredentials(companyKey) {
        const company = this.getCompanyConfig(companyKey);
        if (!company || !company.login) {
            return null;
        }

        return {
            url: company.login.url,
            username: company.login.username,
            password: company.login.password,
            method: company.login.method || 'requests',
            loginData: company.login.login_data || {}
        };
    }

    /**
     * Get payment limits for a company
     */
    getPaymentLimits(companyKey) {
        const company = this.getCompanyConfig(companyKey);
        if (!company || !company.payment) {
            return null;
        }

        return {
            maxAmount: company.payment.max_amount || 100000,
            minAmount: company.payment.min_amount || 1,
            debtTolerance: company.payment.debt_tolerance || 0,
            phonePrefix: company.payment.phone_prefix || '',
            duplicateCheckHours: company.payment.duplicate_check_hours || 0,
            duplicateCheckMinutes: company.payment.duplicate_check_minutes || 0
        };
    }

    /**
     * Get URLs for a company
     */
    getCompanyUrls(companyKey) {
        const company = this.getCompanyConfig(companyKey);
        if (!company) {
            return null;
        }

        return {
            baseUrl: company.urls?.base_url || company.login?.url,
            searchUrl: company.urls?.search_url,
            paymentFormUrl: company.urls?.payment_form_url,
            confirmationUrl: company.urls?.confirmation_url,
            paymentPage: company.urls?.payment_page
        };
    }

    /**
     * Get validation rules for a company
     */
    getValidationRules(companyKey) {
        const company = this.getCompanyConfig(companyKey);
        if (!company || !company.validation) {
            return null;
        }

        return {
            debtRules: {
                tolerance: company.validation.debt_rules?.tolerance || 0,
                allowOverpayment: company.validation.debt_rules?.allow_overpayment || false,
                zeroDebtException: company.validation.debt_rules?.zero_debt_exception || false
            }
        };
    }

    /**
     * Get global settings
     */
    getGlobalSettings() {
        if (!this.config || !this.config.global_settings) {
            return null;
        }

        return this.config.global_settings;
    }

    /**
     * Get API settings
     */
    getApiSettings() {
        const globalSettings = this.getGlobalSettings();
        return globalSettings?.api || null;
    }

    /**
     * Get status codes mapping
     */
    getStatusCodes() {
        const globalSettings = this.getGlobalSettings();
        return globalSettings?.status_codes || {
            "1": "تم الدفع بنجاح",
            "2": "الرقم غير موجود في النظام",
            "3": "فشل في عملية الدفع",
            "4": "المشترك عليه دين"
        };
    }

    /**
     * Get default timeouts
     */
    getDefaultTimeouts() {
        const globalSettings = this.getGlobalSettings();
        return globalSettings?.default_timeouts || {
            login_timeout: 15,
            payment_timeout: 30,
            search_timeout: 10
        };
    }

    /**
     * Get Selenium/Puppeteer settings
     */
    getBrowserSettings() {
        const globalSettings = this.getGlobalSettings();
        const seleniumSettings = globalSettings?.selenium_settings || {};
        
        return {
            headless: seleniumSettings.headless !== false, // Default to true
            windowSize: seleniumSettings.window_size || "1920,1080",
            userAgent: seleniumSettings.user_agent || "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        };
    }

    /**
     * Get all supported companies
     */
    getAllCompanies() {
        if (!this.config || !this.config.companies) {
            return [];
        }

        return Object.entries(this.config.companies).map(([key, config]) => ({
            key,
            productId: config.product_id,
            name: config.name,
            nameEn: config.name_en
        }));
    }

    /**
     * Get all supported product IDs
     */
    getSupportedProductIds() {
        const companies = this.getAllCompanies();
        return companies.map(company => company.productId);
    }

    /**
     * Validate configuration
     */
    validateConfig() {
        if (!this.config) {
            throw new Error('Configuration not loaded');
        }

        if (!this.config.companies) {
            throw new Error('Companies section missing in configuration');
        }

        const requiredFields = ['product_id', 'name', 'login'];
        const errors = [];

        for (const [companyKey, companyConfig] of Object.entries(this.config.companies)) {
            for (const field of requiredFields) {
                if (!(field in companyConfig)) {
                    errors.push(`${companyKey}: Missing required field '${field}'`);
                }
            }

            // Validate login section
            if (companyConfig.login) {
                const requiredLoginFields = ['url', 'username', 'password'];
                for (const field of requiredLoginFields) {
                    if (!(field in companyConfig.login)) {
                        errors.push(`${companyKey}.login: Missing required field '${field}'`);
                    }
                }
            }
        }

        if (errors.length > 0) {
            throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
        }

        log.info('✅ Configuration validation passed');
        return true;
    }

    /**
     * Reload configuration from file
     */
    async reloadConfig() {
        log.info('🔄 Reloading configuration...');
        await this.loadConfig();
        this.validateConfig();
        log.info('✅ Configuration reloaded successfully');
    }
}

// Create singleton instance
const configManager = new ConfigManager();

module.exports = configManager;
