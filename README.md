# TawasOwl Payment API Actor

🚀 **Apify Actor for automated payment processing across multiple Syrian telecom and internet service providers**

## Overview

This Apify Actor automates payment processing for 8 major Syrian telecom and internet service providers:

- **سوا (Sawa)** - Product ID: 29
- **سيرياتيل (Syriatel)** - Product ID: 25  
- **إنت (INET)** - Product ID: 47
- **إم تي إس (MTS)** - Product ID: 48
- **ليما (LEMA)** - Product ID: 49
- **لاينت (Linet)** - Product ID: 46
- **بطاقات (Bitakat)** - Product ID: 50
- **تكامل (Takamol)** - Product ID: 51

## Features

✅ **Multi-Provider Support** - Handles 8 different payment systems  
✅ **Concurrent Processing** - Process multiple payments simultaneously  
✅ **Retry Logic** - Automatic retry on failures  
✅ **Comprehensive Logging** - Detailed logs via Apify Console  
✅ **Error Handling** - Standardized error responses  
✅ **Health Checks** - Monitor system status  
✅ **Configuration Management** - Centralized company settings  

## Usage

### Single Payment

```json
{
  "mode": "single_payment",
  "payment_request": {
    "id": 586316,
    "amount": "24000",
    "number": "7530490",
    "product": 29,
    "type": "2 MB",
    "date": "2025-08-07T08:22:28.000000Z"
  },
  "retry_attempts": 2,
  "timeout_seconds": 120
}
```

### Batch Payment

```json
{
  "mode": "batch_payment",
  "batch_requests": [
    {
      "id": 586316,
      "amount": "24000", 
      "number": "7530490",
      "product": 29,
      "type": "2 MB"
    },
    {
      "id": 586317,
      "amount": "15000",
      "number": "9876543", 
      "product": 25,
      "type": "1 GB"
    }
  ],
  "max_concurrency": 3,
  "retry_attempts": 2
}
```

### Health Check

```json
{
  "mode": "health_check"
}
```

## Response Format

### Success Response
```json
{
  "status": 1,
  "message": "تم الدفع بنجاح",
  "request_data": {...},
  "completion_time": "2025-08-07T08:25:30.123456Z",
  "execution_time": 182.5,
  "company_name": "سوا"
}
```

### Error Response
```json
{
  "status": 3,
  "message": "فشل في عملية الدفع",
  "request_data": {...},
  "completion_time": "2025-08-07T08:25:30.123456Z", 
  "execution_time": 45.2,
  "company_name": "سيرياتيل"
}
```

## Status Codes

| Code | Arabic | English | Description |
|------|--------|---------|-------------|
| 1 | تم الدفع بنجاح | Payment Successful | Payment completed successfully |
| 2 | الرقم غير موجود في النظام | Number Not Found | Customer number not found |
| 3 | فشل في عملية الدفع | Payment Failed | Payment processing failed |
| 4 | المشترك عليه دين | Subscriber Has Debt | Customer has outstanding debt |

## Configuration

The Actor uses `companies_config.json` for company-specific settings:

```json
{
  "companies": {
    "sawa": {
      "product_id": 29,
      "name": "سوا",
      "login": {
        "url": "http://sp.sawaisp.sy/charge_balance_to_customer",
        "username": "your_username",
        "password": "your_password"
      },
      "payment": {
        "max_amount": 100000,
        "min_amount": 1
      }
    }
  }
}
```

## Architecture

```
src/
├── config-manager.js          # Configuration management
├── payment-processors.js      # Main processor coordinator  
└── processors/
    ├── base-processor.js      # Base class for all processors
    ├── sawa-processor.js      # Sawa payment processor
    ├── syriatel-processor.js  # Syriatel payment processor
    ├── isp-care-processor.js  # Base for ISP Care system
    ├── inet-processor.js      # INET processor
    ├── mts-processor.js       # MTS processor
    ├── lema-processor.js      # LEMA processor
    ├── linet-processor.js     # Linet processor
    ├── bitakat-processor.js   # Bitakat processor
    └── takamol-processor.js   # Takamol processor
```

## Payment Processing Methods

### Sawa & Syriatel
- **Method**: Puppeteer (Browser automation)
- **Login**: Web form authentication
- **Process**: Navigate → Login → Search → Pay → Confirm

### ISP Care System (INET, MTS, LEMA, Linet, Bitakat, Takamol)
- **Method**: HTTP requests with session management
- **Login**: POST request authentication  
- **Process**: Login → Search → Extract customer ID → Submit payment

## Error Handling

The Actor implements comprehensive error handling:

- **Network Errors**: Automatic retry with exponential backoff
- **Authentication Failures**: Clear error messages
- **Customer Not Found**: Status code 2 response
- **Payment Failures**: Detailed error information
- **System Errors**: Graceful degradation

## Logging

All operations are logged with different levels:

- **INFO**: Normal operations and status updates
- **WARNING**: Non-critical issues and retries  
- **ERROR**: Critical failures and exceptions

Logs are available in the Apify Console for monitoring and debugging.

## Security

- Credentials stored securely in configuration
- No sensitive data in logs
- Session management for authenticated requests
- Anti-detection measures for browser automation

## Performance

- **Concurrent Processing**: Up to 10 simultaneous payments
- **Timeout Management**: Configurable timeouts per operation
- **Resource Optimization**: Efficient browser and session management
- **Retry Logic**: Smart retry with backoff strategies

## Deployment

1. **Upload to Apify**: Use Apify CLI or web interface
2. **Configure Settings**: Update `companies_config.json` with credentials
3. **Test**: Run health check to verify configuration
4. **Monitor**: Use Apify Console for monitoring and logs

## Development

### Local Testing

```bash
# Install dependencies
npm install

# Run locally
npm start
```

### Adding New Providers

1. Create new processor in `src/processors/`
2. Extend `BaseProcessor` or `IspCareProcessor`
3. Implement `processPayment()` method
4. Add to `payment-processors.js` mapping
5. Update configuration schema

## Support

For issues and questions:
- Check Apify Console logs for detailed error information
- Verify configuration settings
- Test individual processors with health checks
- Monitor network connectivity to provider systems

## License

MIT License - See LICENSE file for details

---

**Built with ❤️ for automated payment processing in Syria**
