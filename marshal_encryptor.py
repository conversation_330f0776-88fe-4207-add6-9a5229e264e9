import marshal
import base64
import zlib
import random
import string

def generate_random_name(length=8):
    """توليد أسماء متغيرات عشوائية"""
    return '_' + ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def encrypt_with_marshal(input_file, output_file):
    """تشفير الملف باستخدام marshal مع طبقات حماية إضافية"""

    print(f"🔄 قراءة الملف: {input_file}")

    # قراءة الكود الأصلي
    with open(input_file, 'r', encoding='utf-8') as f:
        source_code = f.read()

    print("🔄 تجميع الكود...")

    # تجميع الكود إلى bytecode
    compiled_code = compile(source_code, input_file, 'exec')

    print("🔄 تشفير باستخدام marshal...")

    # تحويل إلى marshal
    marshaled_code = marshal.dumps(compiled_code)

    print("🔄 ضغط البيانات...")

    # ضغط البيانات
    compressed_code = zlib.compress(marshaled_code)

    print("🔄 تشفير base64...")

    # تشفير base64
    encoded_code = base64.b64encode(compressed_code).decode('utf-8')

    # تقسيم الكود المشفر إلى أجزاء
    chunks = [encoded_code[i:i+80] for i in range(0, len(encoded_code), 80)]

    # توليد أسماء متغيرات عشوائية
    var1 = generate_random_name()
    var3 = generate_random_name()
    var4 = generate_random_name()
    var5 = generate_random_name()
    var6 = generate_random_name()

    print("🔄 إنشاء الكود المشفر النهائي...")

    # إنشاء الكود المشفر النهائي
    encrypted_code = f'''import marshal, base64, zlib, sys
{var1} = [
'''

    # إضافة الأجزاء المشفرة
    for chunk in chunks:
        encrypted_code += f'    "{chunk}",\n'

    encrypted_code += f''']
try:
    {var3} = "".join({var1})
    {var4} = base64.b64decode({var3})
    {var5} = zlib.decompress({var4})
    {var6} = marshal.loads({var5})
    exec({var6})
except:
    sys.exit()
'''

    print(f"💾 حفظ الملف المشفر: {output_file}")

    # حفظ الملف المشفر
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(encrypted_code)

    print("✅ تم تشفير الملف بنجاح!")
    print(f"📊 حجم الملف الأصلي: {len(source_code)} حرف")
    print(f"📊 حجم الملف المشفر: {len(encrypted_code)} حرف")
    print(f"🔒 مستوى الحماية: عالي جداً (marshal + zlib + base64)")

def main():
    input_file = "payToSawa1.py"
    output_file = "payToSawa1_encrypted_final.py"

    print("🔐 بدء عملية التشفير باستخدام marshal")
    print("=" * 50)

    try:
        encrypt_with_marshal(input_file, output_file)

        print("\n" + "=" * 50)
        print("🎉 تم إنشاء النسخة المشفرة النهائية!")
        print(f"📁 الملف المشفر: {output_file}")
        print("🔒 الكود محمي بـ:")
        print("   ✅ Marshal bytecode compilation")
        print("   ✅ Zlib compression")
        print("   ✅ Base64 encoding")
        print("   ✅ Variable name obfuscation")
        print("   ✅ Clean code without fake functions")
        print("🚀 جاهز للتشغيل مباشرة!")
        print("=" * 50)

    except FileNotFoundError:
        print(f"❌ خطأ: لم يتم العثور على الملف {input_file}")
    except Exception as e:
        print(f"❌ خطأ في التشفير: {e}")

if __name__ == "__main__":
    main()
