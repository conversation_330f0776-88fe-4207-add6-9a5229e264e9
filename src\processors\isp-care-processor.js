const axios = require('axios');
const cheerio = require('cheerio');
const { log } = require('apify').utils;
const configManager = require('../config-manager');
const BaseProcessor = require('./base-processor');

/**
 * ISP Care Base Processor
 * Handles payment processing for companies using ISP Care system
 * (INET, MTS, LEMA, Linet, Bitakat, Takamol)
 */
class IspCareProcessor extends BaseProcessor {
    constructor(companyKey) {
        super(companyKey);
        this.session = null;
    }

    /**
     * Initialize session with login
     */
    async initializeSession() {
        try {
            const credentials = configManager.getLoginCredentials(this.companyKey);
            const urls = configManager.getCompanyUrls(this.companyKey);
            
            if (!credentials || !urls) {
                throw new Error(`Configuration missing for ${this.companyKey}`);
            }

            // Create axios session
            this.session = axios.create({
                timeout: 30000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive'
                }
            });

            // Login
            log.info(`Logging in to ${this.companyName}...`);
            const loginData = {
                username: credentials.username,
                password: credentials.password,
                ...credentials.loginData
            };

            const loginResponse = await this.session.post(credentials.url, loginData);
            
            // Check if login was successful
            if (loginResponse.data.includes('خطأ') || loginResponse.data.includes('error')) {
                throw new Error('Login failed - invalid credentials');
            }

            log.info(`✅ Successfully logged in to ${this.companyName}`);
            return true;

        } catch (error) {
            log.error(`❌ Failed to initialize session for ${this.companyName}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Search for customer by phone number
     */
    async searchCustomer(phoneNumber) {
        try {
            const urls = configManager.getCompanyUrls(this.companyKey);
            const limits = configManager.getPaymentLimits(this.companyKey);
            
            // Format phone number with prefix if needed
            const formattedPhone = limits?.phonePrefix ? 
                limits.phonePrefix + phoneNumber : phoneNumber;

            log.info(`🔍 Searching for customer: ${formattedPhone}`);

            // Search for customer
            const searchUrl = urls.searchUrl.replace('{pg}', '1').replace('{papp}', formattedPhone);
            const searchResponse = await this.session.get(searchUrl);
            
            const $ = cheerio.load(searchResponse.data);
            
            // Check if customer found
            if (searchResponse.data.includes('لا يوجد') || searchResponse.data.includes('not found')) {
                return { found: false, message: 'الرقم غير موجود في النظام' };
            }

            // Extract customer information
            const customerInfo = this.extractCustomerInfo($);
            
            if (!customerInfo.subId) {
                return { found: false, message: 'لم يتم العثور على معرف المشترك' };
            }

            log.info(`✅ Customer found: ${customerInfo.subId}`);
            return { found: true, customerInfo };

        } catch (error) {
            log.error(`❌ Customer search failed: ${error.message}`);
            return { found: false, message: `خطأ في البحث: ${error.message}` };
        }
    }

    /**
     * Extract customer information from search results
     */
    extractCustomerInfo($) {
        try {
            // Look for subscriber ID in various possible locations
            let subId = null;
            
            // Method 1: Look for sub_id in links
            $('a[href*="sub_id"]').each((i, element) => {
                const href = $(element).attr('href');
                const match = href.match(/sub_id=(\d+)/);
                if (match) {
                    subId = match[1];
                    return false; // Break the loop
                }
            });

            // Method 2: Look for subscriber ID in form inputs
            if (!subId) {
                const subIdInput = $('input[name="sub_id"]').val();
                if (subIdInput) {
                    subId = subIdInput;
                }
            }

            // Extract balance/debt information
            let balance = null;
            let debt = null;
            
            const balanceText = $('.balance, .debt, [class*="balance"], [class*="debt"]').text();
            if (balanceText) {
                const numbers = balanceText.match(/\d+(?:\.\d+)?/g);
                if (numbers && numbers.length > 0) {
                    balance = parseFloat(numbers[0]);
                }
            }

            return {
                subId,
                balance,
                debt,
                rawData: $.html()
            };

        } catch (error) {
            log.warning(`Warning extracting customer info: ${error.message}`);
            return { subId: null, balance: null, debt: null };
        }
    }

    /**
     * Process payment for customer
     */
    async processCustomerPayment(customerInfo, amount) {
        try {
            const urls = configManager.getCompanyUrls(this.companyKey);
            
            log.info(`💰 Processing payment of ${amount} for customer ${customerInfo.subId}`);

            // Navigate to payment form
            const paymentFormUrl = urls.paymentFormUrl
                .replace('{pg}', '1')
                .replace('{papp}', customerInfo.subId)
                .replace('{sub_id}', customerInfo.subId);

            const paymentFormResponse = await this.session.get(paymentFormUrl);
            const $ = cheerio.load(paymentFormResponse.data);

            // Prepare payment data
            const paymentData = {
                sub_id: customerInfo.subId,
                amount: amount,
                payment_type: 'cash',
                action: 'add_payment'
            };

            // Extract any additional required fields from the form
            $('input[type="hidden"]').each((i, element) => {
                const name = $(element).attr('name');
                const value = $(element).attr('value');
                if (name && value) {
                    paymentData[name] = value;
                }
            });

            // Submit payment
            const paymentResponse = await this.session.post(urls.confirmationUrl, paymentData);
            
            // Check payment result
            const result = this.parsePaymentResult(paymentResponse.data);
            
            log.info(`Payment result: ${result.success ? 'Success' : 'Failed'} - ${result.message}`);
            return result;

        } catch (error) {
            log.error(`❌ Payment processing failed: ${error.message}`);
            return {
                success: false,
                status: 3,
                message: `خطأ في معالجة الدفع: ${error.message}`
            };
        }
    }

    /**
     * Parse payment result from response
     */
    parsePaymentResult(responseData) {
        try {
            // Check for success indicators
            const successIndicators = ['نجح', 'تم', 'success', 'successful', 'completed'];
            const errorIndicators = ['خطأ', 'فشل', 'error', 'failed', 'invalid'];

            const lowerData = responseData.toLowerCase();

            // Check for success
            for (const indicator of successIndicators) {
                if (lowerData.includes(indicator)) {
                    return {
                        success: true,
                        status: 1,
                        message: 'تم الدفع بنجاح'
                    };
                }
            }

            // Check for specific errors
            if (lowerData.includes('غير موجود') || lowerData.includes('not found')) {
                return {
                    success: false,
                    status: 2,
                    message: 'الرقم غير موجود في النظام'
                };
            }

            if (lowerData.includes('دين') || lowerData.includes('debt')) {
                return {
                    success: false,
                    status: 4,
                    message: 'المشترك عليه دين'
                };
            }

            // Check for general errors
            for (const indicator of errorIndicators) {
                if (lowerData.includes(indicator)) {
                    return {
                        success: false,
                        status: 3,
                        message: 'فشل في عملية الدفع'
                    };
                }
            }

            // Default to success if no clear error indicators
            return {
                success: true,
                status: 1,
                message: 'تم الدفع بنجاح'
            };

        } catch (error) {
            return {
                success: false,
                status: 3,
                message: `خطأ في تحليل النتيجة: ${error.message}`
            };
        }
    }

    /**
     * Main payment processing method
     */
    async processPayment(order) {
        try {
            log.info(`🔄 Starting ${this.companyName} payment processing for order ${order.id}`);

            // Validate order
            this.validateOrder(order);

            // Check payment limits
            const limitCheck = this.checkPaymentLimits(order.amount);
            if (!limitCheck.valid) {
                return this.createErrorResponse(3, limitCheck.error);
            }

            // Initialize session
            await this.initializeSession();

            // Search for customer
            const searchResult = await this.searchCustomer(order.phone_number);
            if (!searchResult.found) {
                const status = searchResult.message.includes('غير موجود') ? 2 : 3;
                return this.createErrorResponse(status, searchResult.message);
            }

            // Process payment
            const paymentResult = await this.processCustomerPayment(searchResult.customerInfo, order.amount);
            
            // Log payment details
            this.logPaymentDetails(
                order.id, 
                order.phone_number, 
                order.amount, 
                paymentResult.status, 
                paymentResult.message
            );

            return {
                status: paymentResult.status,
                message: paymentResult.message
            };

        } catch (error) {
            log.error(`❌ ${this.companyName} payment processing failed for order ${order.id}: ${error.message}`);
            this.logPaymentDetails(order.id, order.phone_number, order.amount, 3, error.message);
            return this.createErrorResponse(3, `خطأ في معالجة الطلب: ${error.message}`);
        }
    }

    /**
     * Health check
     */
    async healthCheck() {
        try {
            const credentials = configManager.getLoginCredentials(this.companyKey);
            const urls = configManager.getCompanyUrls(this.companyKey);
            
            if (!credentials || !urls) {
                return { healthy: false, details: 'Configuration missing' };
            }

            return { 
                healthy: true, 
                details: 'Configuration loaded',
                hasCredentials: !!credentials,
                hasUrls: !!urls
            };

        } catch (error) {
            return { healthy: false, details: error.message };
        }
    }
}

module.exports = IspCareProcessor;
