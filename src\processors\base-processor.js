const { log } = require('apify').utils;
const configManager = require('../config-manager');

/**
 * Base Payment Processor
 * Provides common functionality for all payment processors
 */
class BaseProcessor {
    constructor(companyKey) {
        this.companyKey = companyKey;
        this.companyName = '';
        this.productId = null;
        this.initialized = false;
    }

    /**
     * Initialize the processor
     */
    async initialize() {
        try {
            const companyConfig = configManager.getCompanyConfig(this.companyKey);
            if (!companyConfig) {
                throw new Error(`Configuration not found for company: ${this.companyKey}`);
            }

            this.companyName = companyConfig.name || this.companyKey;
            this.productId = companyConfig.product_id;
            this.initialized = true;

            log.info(`✅ ${this.companyName} processor initialized`);
            return true;

        } catch (error) {
            log.error(`❌ Failed to initialize ${this.companyKey} processor: ${error.message}`);
            throw error;
        }
    }

    /**
     * Random sleep utility
     */
    async randomSleep(minMs = 500, maxMs = 1500) {
        const sleepTime = Math.floor(Math.random() * (maxMs - minMs + 1)) + minMs;
        await new Promise(resolve => setTimeout(resolve, sleepTime));
    }

    /**
     * Validate order data
     */
    validateOrder(order) {
        const requiredFields = ['id', 'phone_number', 'amount'];
        const errors = [];

        for (const field of requiredFields) {
            if (!(field in order) || order[field] === null || order[field] === undefined) {
                errors.push(`Missing required field: ${field}`);
            }
        }

        // Validate amount
        if (order.amount !== undefined) {
            const amount = parseFloat(order.amount);
            if (isNaN(amount) || amount <= 0) {
                errors.push('Amount must be a positive number');
            }
        }

        // Validate phone number
        if (order.phone_number !== undefined && !String(order.phone_number).trim()) {
            errors.push('Phone number cannot be empty');
        }

        if (errors.length > 0) {
            throw new Error(`Order validation failed: ${errors.join(', ')}`);
        }

        return true;
    }

    /**
     * Check payment limits
     */
    checkPaymentLimits(amount) {
        const limits = configManager.getPaymentLimits(this.companyKey);
        if (!limits) {
            return { valid: true, error: null };
        }

        if (amount < limits.minAmount) {
            return {
                valid: false,
                error: `المبلغ أقل من الحد الأدنى المسموح (${limits.minAmount})`
            };
        }

        if (amount > limits.maxAmount) {
            return {
                valid: false,
                error: `المبلغ أكبر من الحد الأقصى المسموح (${limits.maxAmount})`
            };
        }

        return { valid: true, error: null };
    }

    /**
     * Format phone number according to company requirements
     */
    formatPhoneNumber(phoneNumber) {
        const limits = configManager.getPaymentLimits(this.companyKey);
        const prefix = limits?.phonePrefix || '';
        
        let formattedNumber = String(phoneNumber).trim();
        
        // Add prefix if required and not already present
        if (prefix && !formattedNumber.startsWith(prefix)) {
            formattedNumber = prefix + formattedNumber;
        }

        return formattedNumber;
    }

    /**
     * Log payment details
     */
    logPaymentDetails(orderId, phoneNumber, amount, status, details = '') {
        const timestamp = new Date().toISOString();
        log.info(`[PAYMENT] Order: ${orderId}, Phone: ${phoneNumber}, Amount: ${amount}, Status: ${status}, Details: ${details}`, {
            orderId,
            phoneNumber,
            amount,
            status,
            details,
            timestamp,
            company: this.companyName
        });
    }

    /**
     * Create standardized error response
     */
    createErrorResponse(status, message, details = null) {
        return {
            status,
            message,
            details,
            timestamp: new Date().toISOString(),
            company: this.companyName
        };
    }

    /**
     * Create standardized success response
     */
    createSuccessResponse(message = 'تم الدفع بنجاح', details = null) {
        return {
            status: 1,
            message,
            details,
            timestamp: new Date().toISOString(),
            company: this.companyName
        };
    }

    /**
     * Handle common browser setup
     */
    async setupBrowser(puppeteer) {
        const browserSettings = configManager.getBrowserSettings();
        
        const browser = await puppeteer.launch({
            headless: browserSettings.headless,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--window-size=1920,1080'
            ]
        });

        const page = await browser.newPage();
        
        // Set user agent
        await page.setUserAgent(browserSettings.userAgent);
        
        // Set viewport
        await page.setViewport({ width: 1920, height: 1080 });

        // Anti-detection measures
        await page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en', 'ar'] });
            Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5] });
            Object.defineProperty(navigator, 'platform', { get: () => 'Win32' });
            Object.defineProperty(navigator, 'hardwareConcurrency', { get: () => 4 });
            window.chrome = { runtime: {} };
        });

        return { browser, page };
    }

    /**
     * Safe browser cleanup
     */
    async cleanupBrowser(browser, page) {
        try {
            if (page) {
                await page.close();
            }
            if (browser) {
                await browser.close();
            }
            log.info(`🧹 Browser cleanup completed for ${this.companyName}`);
        } catch (error) {
            log.warning(`Warning during browser cleanup for ${this.companyName}: ${error.message}`);
        }
    }

    /**
     * Wait for element with timeout and retry
     */
    async waitForElementSafe(page, selector, timeout = 10000, retries = 3) {
        for (let i = 0; i < retries; i++) {
            try {
                await page.waitForSelector(selector, { timeout });
                return true;
            } catch (error) {
                if (i === retries - 1) {
                    throw new Error(`Element not found after ${retries} attempts: ${selector}`);
                }
                log.warning(`Attempt ${i + 1} failed to find element: ${selector}, retrying...`);
                await this.randomSleep(1000, 2000);
            }
        }
        return false;
    }

    /**
     * Type text with human-like delays
     */
    async typeHumanLike(page, selector, text, delay = 100) {
        const element = await page.$(selector);
        if (!element) {
            throw new Error(`Element not found: ${selector}`);
        }

        await element.click({ clickCount: 3 }); // Select all existing text
        await this.randomSleep(100, 300);

        for (const char of text) {
            await element.type(char);
            await this.randomSleep(delay * 0.5, delay * 1.5);
        }
    }

    /**
     * Check for common error messages on page
     */
    async checkForErrors(page) {
        const errorSelectors = [
            '.error',
            '.alert-error',
            '.alert-danger',
            '[class*="error"]',
            '.message.error',
            '#error-message'
        ];

        for (const selector of errorSelectors) {
            try {
                const errorElement = await page.$(selector);
                if (errorElement) {
                    const errorText = await page.evaluate(el => el.textContent.trim(), errorElement);
                    if (errorText) {
                        return errorText;
                    }
                }
            } catch (error) {
                // Continue checking other selectors
            }
        }

        return null;
    }

    /**
     * Check for success messages on page
     */
    async checkForSuccess(page) {
        const successSelectors = [
            '.success',
            '.alert-success',
            '[class*="success"]',
            '.message.success',
            '#success-message'
        ];

        for (const selector of successSelectors) {
            try {
                const successElement = await page.$(selector);
                if (successElement) {
                    const successText = await page.evaluate(el => el.textContent.trim(), successElement);
                    if (successText) {
                        return successText;
                    }
                }
            } catch (error) {
                // Continue checking other selectors
            }
        }

        return null;
    }

    /**
     * Abstract method - must be implemented by subclasses
     */
    async processPayment(order) {
        throw new Error('processPayment method must be implemented by subclass');
    }

    /**
     * Abstract method - can be implemented by subclasses
     */
    async healthCheck() {
        return { healthy: true, details: 'Base health check passed' };
    }
}

module.exports = BaseProcessor;
