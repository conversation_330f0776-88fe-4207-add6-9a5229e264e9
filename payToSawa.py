import requests
import time
import urllib3
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.alert import Alert
from fake_useragent import UserAgent
import random
import os

import sys

# Import configuration manager
try:
    from config_manager import get_config
    config_manager = get_config()
    SAWA_CONFIG = config_manager.get_company_config('sawa')
    if not SAWA_CONFIG:
        print("❌ Warning: Sawa configuration not found in config file, using hardcoded values")
        SAWA_CONFIG = None
except ImportError:
    print("❌ Warning: config_manager not available, using hardcoded values")
    SAWA_CONFIG = None


# تعطيل تحذيرات SSL الغير مهمة
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_sawa_setting(setting_path, default_value):
    """
    Get Sawa setting from config with fallback to default value

    Args:
        setting_path: Dot-separated path to setting (e.g., 'login.username')
        default_value: Default value if setting not found

    Returns:
        Setting value or default value
    """
    if not SAWA_CONFIG:
        return default_value

    try:
        keys = setting_path.split('.')
        value = SAWA_CONFIG
        for key in keys:
            value = value[key]
        return value
    except (KeyError, TypeError):
        return default_value



# License verification removed - running in standalone mode

# API URLs (kept for reference but not used in single order mode)
# url = "https://menfax.com/pos/public/api/orders"
# status_url = "https://menfax.com/pos/public/api/change-order-status"
# secret = "SECRET1265AQREFGHKLFS!@#"

# Logging disabled - no file logging in standalone mode
def log(message, log_type="INFO"):
    """
    Simple console logging only - no file logging
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    full_message = f"[{timestamp}] [{log_type}] {message}"
    print(full_message)

def log_payment_details(order_id, phone_number, amount, status, details=""):
    """
    Payment details logging disabled - console only
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [PAYMENT] Order: {order_id}, Phone: {phone_number}, Amount: {amount}, Status: {status}, Details: {details}")

def check_duplicate_payment(phone_number, amount):
    """
    Check if the same amount was paid for the same number within 24 hours
    Args:
        phone_number: Customer phone number
        amount: Payment amount
    Returns:
        bool: True if duplicate found, False otherwise
    """
    if not os.path.exists("payment_tracking.txt"):
        log("No payment tracking file found - no duplicates possible", "INFO")
        return False

    current_time = datetime.now()
    twenty_four_hours_ago = current_time - timedelta(hours=24)

    try:
        with open("payment_tracking.txt", "r", encoding="utf-8") as f:
            for line in f:
                parts = line.strip().split("|")
                if len(parts) >= 5:
                    payment_time = datetime.strptime(parts[0], "%Y-%m-%d %H:%M:%S")
                    payment_phone = parts[2]
                    payment_amount = parts[3]
                    payment_status = parts[4]

                    # Check if payment was successful and within 24 hours
                    if (payment_time >= twenty_four_hours_ago and
                        payment_phone == phone_number and
                        payment_amount == amount and
                        payment_status == "1"):
                        log(f"Duplicate payment found: {phone_number} - {amount} at {parts[0]}", "WARNING")
                        return True

        log(f"No duplicate payment found for {phone_number} - {amount} within 24 hours", "INFO")
    except Exception as e:
        log(f"Error checking duplicate payments: {e}", "ERROR")

    return False

def random_sleep(min_sec=0.5, max_sec=1.5):
    """
    Sleep for a random duration between min_sec and max_sec seconds
    Args:
        min_sec: Minimum sleep duration
        max_sec: Maximum sleep duration
    """
    time.sleep(random.uniform(min_sec, max_sec))

def extract_debt_amount(balance_text):
    """
    Extract debt amount from Arabic balance text using multiple patterns
    Args:
        balance_text: Arabic text like "رصيد المشترك 23999.21ل. س عليه" or "رصيد المشترك 3300.42ل. س عليه"
    Returns:
        float: Debt amount or -1 if extraction fails (to indicate parsing error)
    """
    try:
        import re

        # Pattern 1: Numbers before "ل. س" (with space)
        pattern1 = r'(\d+(?:\.\d+)?)\s*ل\.\s*س'
        match1 = re.search(pattern1, balance_text)
        if match1:
            return float(match1.group(1))

        # Pattern 2: Numbers before "ل.س" (without space)
        pattern2 = r'(\d+(?:\.\d+)?)ل\.س'
        match2 = re.search(pattern2, balance_text)
        if match2:
            return float(match2.group(1))

        # If no pattern matches, return -1 to indicate parsing failure
        log(f"Could not extract debt amount from text: '{balance_text}'", "ERROR")
        return -1.0

    except Exception as e:
        log(f"Error extracting debt amount from text '{balance_text}': {e}", "ERROR")
        return -1.0

def validate_debt_payment(debt_amount, payment_amount):
    """
    Validate payment against debt amount based on new rules
    Args:
        debt_amount: Subscriber's debt amount
        payment_amount: Amount to be paid
    Returns:
        tuple: (is_valid, reason_message)
    """
    try:
        debt_amount = float(debt_amount)
        payment_amount = float(payment_amount)

        # Calculate absolute difference
        difference = abs(debt_amount - payment_amount)

        # Rule 1: If absolute difference <= 140 → accept (except when debt is zero)
        if difference <= 140 and debt_amount != 0:
            return True, f"تم قبول الدفع - الفرق ضمن الحد المسموح"

        # Rule 2: If debt <= payment amount → accept
        if debt_amount <= payment_amount:
            return True, f"تم قبول الدفع - المبلغ كافي لتغطية الدين"

        # Rule 3: If debt > payment amount and difference > 140 → reject
        if debt_amount > payment_amount:
            return False, f"المبلغ المطلوب: {debt_amount:.0f} ليرة سورية"

        return False, f"المبلغ المطلوب: {debt_amount:.0f} ليرة سورية"

    except Exception as e:
        log(f"Error validating debt payment: {e}", "ERROR")
        return True, "خطأ في التحقق من الدين - السماح بالدفع"

def process_single_payment(driver, order, retry_with_extra=False):
    """
    Process a single payment order with enhanced error handling and validation
    Args:
        driver: Selenium WebDriver instance
        order: Order data from API
        processed_order_ids: Set of already processed order IDs
        retry_with_extra: Whether to add 1 to amount for duplicate handling
    Returns:
        bool: True if payment was processed successfully, False otherwise
    """
    order_type = order.get("type", "")
    number = order["phone_number"]
    order_id = str(order.get("id"))
    original_amount = order["amount"]

    # Add 1 to amount if this is a retry for duplicate handling
    amount = str(int(original_amount) + 1) if retry_with_extra else original_amount

    log(f"Processing order ID: {order_id}, Phone: {number}, Amount: {amount}", "INFO")

    try:
        # Navigate to payment page
        log("Navigating to payment page", "INFO")
        driver.get("http://sp.sawaisp.sy/charge_balance_to_customer")
        WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
        random_sleep()

        # Enter phone number
        log(f"Entering phone number: {number}", "INFO")
        phone_input = driver.find_element(By.NAME, "phone_number")
        phone_input.click()
        random_sleep(0.3, 0.8)
        phone_input.clear()
        phone_input.send_keys(number)
        random_sleep()

        # Select region (041 Lattakia)
        log("Selecting region: 041 (Lattakia)", "INFO")
        driver.find_element(By.CLASS_NAME, "select2-choice").click()
        random_sleep(0.5, 1.0)
        driver.find_element(By.ID, "s2id_autogen1_search").send_keys("041 (اللاذقية)")
        random_sleep(1.0, 1.5)
        driver.find_element(By.XPATH, "//*[text()='041 (اللاذقية)']").click()
        random_sleep()

        # Check if number exists in system
        try:
            error_element = WebDriverWait(driver, 3).until(
                EC.presence_of_element_located((By.XPATH, "/html/body/section/div[2]/div[3]/div/span"))
            )
            error_text = error_element.text.strip()

            if "لا يوجد بيانات مطابقة" in error_text:
                log(f"Number not found in system: {number}", "ERROR")
                log_payment_details(order_id, number, amount, "2", "Number not found in system")

                # Display result (number not found)
                display_payment_result(order_id, 2, "الرقم غير موجود في النظام")
                return False

        except:
            # No error message found, continue with payment
            pass

        # Check package price for MB orders (speed validation)
        if "MB" in order_type:
            log("Checking package price for MB order", "INFO")
            try:
                price_element = WebDriverWait(driver, 10).until(
                    lambda d: d.find_element(By.ID, "member_pkg_price") if d.find_element(By.ID,"member_pkg_price").text.strip() != "" else False
                )
                price_text = price_element.text.strip()
                price_digits = ''.join(filter(str.isdigit, price_text))
                log(f"Package price displayed: {price_text} → Extracted amount: {price_digits}", "INFO")

                # Check if paid speed is less than package speed (reject only if paid < package)
                if int(original_amount) < int(price_digits):
                    log(f"Payment rejected: Paid amount ({original_amount}) is less than package price ({price_digits})", "WARNING")
                    log_payment_details(order_id, number, amount, "3", f"Paid amount less than package price: {original_amount} < {price_digits}")

                    # Display result (payment failed due to wrong amount)
                    display_payment_result(order_id, 3, f"المبلغ المدفوع ({original_amount}) أقل من سعر الحزمة ({price_digits})")
                    return False
                else:
                    log(f"Payment accepted: Paid amount ({original_amount}) >= package price ({price_digits})", "SUCCESS")

            except Exception as e:
                log(f"Error checking package price: {e}", "ERROR")

        # Check for amount over maximum limit (from config)
        max_amount = get_sawa_setting('payment.max_amount', 100000)
        if int(original_amount) > max_amount:
            log(f"Payment rejected: Amount exceeds {max_amount} limit ({original_amount})", "WARNING")
            log_payment_details(order_id, number, amount, "3", f"Amount exceeds {max_amount} limit: {original_amount}")

            # Display result (payment failed due to wrong amount)
            display_payment_result(order_id, 3, f"المبلغ ({original_amount}) يتجاوز الحد الأقصى المسموح ({max_amount:,})")
            return False

        # Check subscriber balance and debt status
        try:
            balance_element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "member_balance"))
            )
            balance_text = balance_element.text.strip()
            log(f"Subscriber balance info: {balance_text}", "INFO")

            # Check debt status - only validate if subscriber has debt ("عليه")
            if "عليه" in balance_text:
                # Extract debt amount from balance text
                debt_amount = extract_debt_amount(balance_text)
                log(f"Extracted debt amount: {debt_amount}", "INFO")

                # Check if debt extraction failed
                if debt_amount == -1.0:
                    log(f"Failed to extract debt amount from balance text: {balance_text}", "ERROR")
                    log_payment_details(order_id, number, amount, "3", f"Could not parse debt amount from: {balance_text}")

                    # Display result (error processing debt data)
                    display_payment_result(order_id, 3, "خطأ غير معروف في معالجة بيانات الدين")
                    return False

                # Validate payment against debt amount using new rules
                is_valid, reason = validate_debt_payment(debt_amount, original_amount)

                if not is_valid:
                    log(f"Payment rejected: {reason} - {balance_text}", "WARNING")
                    log_payment_details(order_id, number, amount, "4", f"Debt validation failed: {reason}")

                    # Display result (subscriber has debt)
                    display_payment_result(order_id, 4, reason)
                    return False
                else:
                    log(f"Payment accepted: {reason} - {balance_text}", "SUCCESS")

            elif "له" in balance_text:
                log(f"Payment accepted: Subscriber has credit balance (company owes subscriber) - {balance_text}", "SUCCESS")
            else:
                log(f"Warning: Could not determine debt status from balance text: {balance_text}", "WARNING")

        except Exception as e:
            log(f"Error checking subscriber balance: {e}", "ERROR")
            # Continue with payment if balance check fails
            pass

        # Check if we can determine both package and debt information
        # If neither can be determined, the number might be invalid
        package_info_available = False
        debt_info_available = False

        try:
            # Check if package price is available (for MB orders)
            if "MB" in order_type:
                try:
                    price_element = driver.find_element(By.ID, "member_pkg_price")
                    if price_element.text.strip():
                        package_info_available = True
                except:
                    pass
            else:
                # For non-MB orders, assume package info is available
                package_info_available = True

            # Check if debt/balance info is available
            try:
                balance_element = driver.find_element(By.ID, "member_balance")
                balance_text = balance_element.text.strip()
                if balance_text and ("عليه" in balance_text or "له" in balance_text):
                    debt_info_available = True
            except:
                pass

            # If we cannot determine both package and debt info, number might be invalid
            if not package_info_available and not debt_info_available:
                log(f"Cannot determine both package and debt information for number {number} - number might be invalid", "ERROR")
                log_payment_details(order_id, number, amount, "2", "Cannot determine package and debt info - invalid number")

                # Display result (number not found)
                display_payment_result(order_id, 2, "خطأ الرقم أو الشركة")
                return False

        except Exception as e:
            log(f"Error checking number validity: {e}", "ERROR")
            # Continue with payment if validity check fails
            pass

        # Check for duplicate payment within 24 hours
        if not retry_with_extra and check_duplicate_payment(number, original_amount):
            log(f"Duplicate payment detected for {number} with amount {original_amount} within 24 hours", "WARNING")
            log(f"Retrying with amount + 1: {int(original_amount) + 1}", "INFO")

            # Retry with amount + 1 (don't send API rejection yet)
            return process_single_payment(driver, order, retry_with_extra=True)

        # Get balance before payment
        log("Getting balance before payment", "INFO")
        li_element = driver.find_element(By.XPATH, "//li[contains(text(),'الرصيد')]")
        balance_before = li_element.text.split(":")[1].strip() if ":" in li_element.text else "0"
        log(f"Balance before payment: {balance_before}", "INFO")
        random_sleep()

        # Enter payment amount
        log(f"Entering payment amount: {amount}", "INFO")
        amount_input = driver.find_element(By.ID, "amount")
        amount_input.clear()
        amount_input.send_keys(amount)
        random_sleep()

        # Submit payment
        log("Submitting payment", "INFO")
        driver.find_element(By.ID, "charge_balance_to_customer_submitter").click()
        random_sleep()

        # Handle any alerts
        try:
            WebDriverWait(driver, 5).until(EC.alert_is_present())
            alert = Alert(driver)
            alert.accept()
            log("Alert dismissed", "INFO")
            random_sleep(0.5, 1)
        except:
            log("No alert present", "INFO")

        # Wait before checking result
        time.sleep(2)

        # Check for payment error messages before refreshing
        try:
            error_element = driver.find_element(By.XPATH, "/html/body/section/div[2]/div[3]/div/span")
            error_text = error_element.text.strip()

            if "خطأ في عملية شحن الرصيد،" in error_text:
                log(f"Payment error detected: {error_text}", "ERROR")

                if not retry_with_extra:
                    log("Retrying payment with amount + 1 due to payment error", "INFO")
                    return process_single_payment(driver, order, retry_with_extra=True)
                else:
                    log("Payment failed even with retry (+1 amount)", "ERROR")
                    log_payment_details(order_id, number, amount, "3", f"Payment error after retry: {error_text}")
                    display_payment_result(order_id, 3, "فشل في عملية الدفع حتى بعد المحاولة الثانية")
                    return False
        except:
            # No error message found, continue
            pass

        # Refresh page and check balance after payment
        log("Refreshing page to check payment result", "INFO")
        driver.refresh()
        WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
        random_sleep()

        # Get balance after payment
        li_element_after = driver.find_element(By.XPATH, "//li[contains(text(),'الرصيد')]")
        balance_after = li_element_after.text.split(":")[1].strip() if ":" in li_element_after.text else "0"
        log(f"Balance after payment: {balance_after}", "INFO")

        # Determine payment status
        payment_successful = balance_before != balance_after

        if payment_successful:
            log(f"Payment successful for order {order_id}", "SUCCESS")
            log_payment_details(order_id, number, amount, "1", f"Balance changed from {balance_before} to {balance_after}")
            # Display success result
            display_payment_result(order_id, 1)
        else:
            log(f"Payment failed for order {order_id} - balance unchanged", "ERROR")

            # If this is the first attempt and balance didn't change, try with +1
            if not retry_with_extra:
                log("Payment failed - retrying with amount + 1", "INFO")
                return process_single_payment(driver, order, retry_with_extra=True)
            else:
                # This was already a retry, so send failure status
                log("Payment failed even with retry (+1 amount) - balance unchanged", "ERROR")
                log_payment_details(order_id, number, amount, "3", f"Balance unchanged after retry: {balance_before}")
                display_payment_result(order_id, 3, "فشل الدفع - لم يتغير الرصيد حتى بعد المحاولة الثانية")

        return payment_successful

    except Exception as e:
        log(f"Error processing payment for order {order_id}: {e}", "ERROR")
        log_payment_details(order_id, number, amount, "3", f"Processing error: {str(e)}")

        # Display error result
        display_payment_result(order_id, 3, f"خطأ في معالجة الطلب: {str(e)}")
        return False

def display_payment_result(order_id, status, message=None):
    """
    Display payment result to user (no API sending)
    Args:
        order_id: Order ID
        status: Status code (1=paid, 2=number not found, 3=payment failed, 4=subscriber has debt)
        message: Optional Arabic message for rejection cases
    """
    status_descriptions = {
        1: "✅ تم الدفع بنجاح",
        2: "❌ الرقم غير موجود في النظام",
        3: "❌ فشل في عملية الدفع",
        4: "❌ المشترك عليه دين"
    }

    print("\n" + "="*50)
    print("📋 نتيجة معالجة الطلب")
    print("="*50)
    print(f"رقم الطلب: {order_id}")
    print(f"حالة الطلب: {status}")
    print(f"الوصف: {status_descriptions.get(status, 'حالة غير معروفة')}")
    if message:
        print(f"رسالة إضافية: {message}")
    print("="*50)

    log(f"Payment result displayed for order {order_id} with status {status}", "INFO")

def process_single_payment_sawa(order):
    """
    API wrapper for Sawa payment processing

    Args:
        order: Order object with id, phone_number, amount, type

    Returns:
        tuple: (success, status, message)
    """
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from selenium_stealth import stealth

        # Initialize browser with enhanced stealth settings
        log("Initializing browser with stealth settings", "INFO")
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-images")
        chrome_options.add_argument("--disable-javascript")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")

        # Create driver
        driver = webdriver.Chrome(options=chrome_options)

        # Apply stealth settings
        stealth(driver,
                languages=["en-US", "en"],
                vendor="Google Inc.",
                platform="Win32",
                webgl_vendor="Intel Inc.",
                renderer="Intel Iris OpenGL Engine",
                fix_hairline=True)

        try:
            # Process the payment
            success = process_single_payment(driver, order)

            if success:
                return True, 1, "تم الدفع بنجاح"
            else:
                return False, 3, "فشل في عملية الدفع"

        finally:
            # Clean up browser
            try:
                driver.quit()
                log("Browser closed successfully", "INFO")
            except:
                pass

    except Exception as e:
        log(f"Error in process_single_payment_sawa: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_single_payment_sawa_api(order):
    """
    API wrapper for Sawa payment processing that returns detailed status information

    Args:
        order: Order object with id, phone_number, amount, type

    Returns:
        tuple: (success, status, message)
    """
    order_type = order.get("type", "")
    number = order["phone_number"]
    order_id = str(order.get("id"))
    original_amount = order["amount"]

    log(f"Processing Sawa order ID: {order_id}, Phone: {number}, Amount: {original_amount}", "INFO")

    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from selenium_stealth import stealth

        # Chrome options for headless operation
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")

        # Create driver
        driver = webdriver.Chrome(options=chrome_options)

        # Apply stealth settings
        stealth(driver,
                languages=["en-US", "en"],
                vendor="Google Inc.",
                platform="Win32",
                webgl_vendor="Intel Inc.",
                renderer="Intel Iris OpenGL Engine",
                fix_hairline=True)

        try:
            # Navigate to payment page (URL from config)
            login_url = get_sawa_setting('login.url', 'http://sp.sawaisp.sy/charge_balance_to_customer')
            log(f"Navigating to payment page: {login_url}", "INFO")
            driver.get(login_url)
            WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
            random_sleep()

            # Enter phone number
            log(f"Entering phone number: {number}", "INFO")
            phone_input = driver.find_element(By.NAME, "phone_number")
            phone_input.click()
            random_sleep(0.3, 0.8)
            phone_input.clear()
            phone_input.send_keys(number)
            random_sleep()

            # Click search button
            log("Clicking search button", "INFO")
            driver.find_element(By.ID, "search_customer").click()
            random_sleep()

            # Check if number exists in system
            try:
                error_element = WebDriverWait(driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, "/html/body/section/div[2]/div[3]/div/span"))
                )
                error_text = error_element.text.strip()

                if "لا يوجد بيانات مطابقة" in error_text:
                    log(f"Number not found in system: {number}", "ERROR")
                    log_payment_details(order_id, number, original_amount, "2", "Number not found in system")
                    return False, 2, "الرقم غير موجود في النظام"

            except:
                # No error message found, continue with payment
                pass

            # Check subscriber balance and debt status
            try:
                balance_element = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.ID, "member_balance"))
                )
                balance_text = balance_element.text.strip()
                log(f"Subscriber balance info: {balance_text}", "INFO")

                # Check debt status - only validate if subscriber has debt ("عليه")
                if "عليه" in balance_text:
                    # Extract debt amount from balance text
                    debt_amount = extract_debt_amount(balance_text)
                    log(f"Extracted debt amount: {debt_amount}", "INFO")

                    # Check if debt extraction failed
                    if debt_amount == -1.0:
                        log(f"Failed to extract debt amount from balance text: {balance_text}", "ERROR")
                        log_payment_details(order_id, number, original_amount, "3", f"Could not parse debt amount from: {balance_text}")
                        return False, 3, "خطأ غير معروف في معالجة بيانات الدين"

                    # Validate payment against debt amount using new rules
                    is_valid, reason = validate_debt_payment(debt_amount, original_amount)

                    if not is_valid:
                        log(f"Payment rejected: {reason} - {balance_text}", "WARNING")
                        log_payment_details(order_id, number, original_amount, "4", f"Debt validation failed: {reason}")
                        return False, 4, reason
                    else:
                        log(f"Payment accepted: {reason} - {balance_text}", "SUCCESS")

                elif "له" in balance_text:
                    log(f"Payment accepted: Subscriber has credit balance (company owes subscriber) - {balance_text}", "SUCCESS")
                else:
                    log(f"Warning: Could not determine debt status from balance text: {balance_text}", "WARNING")

            except Exception as e:
                log(f"Error checking balance: {e}", "ERROR")
                # Continue with payment if balance check fails
                pass

            # Check for amount over maximum limit (from config)
            max_amount = get_sawa_setting('payment.max_amount', 100000)
            if int(original_amount) > max_amount:
                log(f"Payment rejected: Amount exceeds {max_amount} limit ({original_amount})", "WARNING")
                log_payment_details(order_id, number, original_amount, "3", f"Amount exceeds {max_amount} limit: {original_amount}")
                return False, 3, f"المبلغ ({original_amount}) يتجاوز الحد الأقصى المسموح ({max_amount:,})"

            # Check for duplicate payment within 24 hours
            if check_duplicate_payment(number, original_amount):
                log(f"Duplicate payment detected for {number} with amount {original_amount} within 24 hours", "WARNING")
                # Try with amount + 1
                amount = str(int(original_amount) + 1)
                log(f"Retrying with amount + 1: {amount}", "INFO")
            else:
                amount = str(original_amount)

            # Get balance before payment
            log("Getting balance before payment", "INFO")
            li_element = driver.find_element(By.XPATH, "//li[contains(text(),'الرصيد')]")
            balance_before = li_element.text.split(":")[1].strip() if ":" in li_element.text else "0"
            log(f"Balance before payment: {balance_before}", "INFO")
            random_sleep()

            # Enter payment amount
            log(f"Entering payment amount: {amount}", "INFO")
            amount_input = driver.find_element(By.ID, "amount")
            amount_input.clear()
            amount_input.send_keys(amount)
            random_sleep()

            # Submit payment
            log("Submitting payment", "INFO")
            driver.find_element(By.ID, "charge_balance_to_customer_submitter").click()
            random_sleep()

            # Check for payment error messages before refreshing
            try:
                error_element = driver.find_element(By.XPATH, "/html/body/section/div[2]/div[3]/div/span")
                error_text = error_element.text.strip()

                if "خطأ في عملية شحن الرصيد،" in error_text:
                    log(f"Payment error detected: {error_text}", "ERROR")
                    log_payment_details(order_id, number, amount, "3", f"Payment error: {error_text}")
                    return False, 3, "فشل في عملية الدفع"
            except:
                # No error message found, continue
                pass

            # Refresh page and check balance after payment
            log("Refreshing page to check balance after payment", "INFO")
            driver.refresh()
            random_sleep()

            # Get balance after payment
            li_element = driver.find_element(By.XPATH, "//li[contains(text(),'الرصيد')]")
            balance_after = li_element.text.split(":")[1].strip() if ":" in li_element.text else "0"
            log(f"Balance after payment: {balance_after}", "INFO")

            # Determine payment success based on balance change
            payment_successful = balance_before != balance_after
            log(f"Balance comparison: {balance_before} -> {balance_after}, Changed: {payment_successful}", "INFO")

            if payment_successful:
                log(f"Sawa payment successful for order {order_id}", "SUCCESS")
                log_payment_details(order_id, number, amount, "1", f"Balance changed from {balance_before} to {balance_after}")
                return True, 1, "تم الدفع بنجاح"
            else:
                log(f"Sawa payment failed for order {order_id} - balance unchanged", "ERROR")
                log_payment_details(order_id, number, amount, "3", f"Balance unchanged: {balance_before}")
                return False, 3, "فشل الدفع - لم يتغير الرصيد"

        finally:
            # Clean up browser
            try:
                driver.quit()
                log("Browser closed successfully", "INFO")
            except:
                pass

    except Exception as e:
        log(f"Error in process_single_payment_sawa_api: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_single_order_from_input():
    """
    Process a single order from user input instead of fetching from API
    """
    try:
        # Initialize browser with enhanced stealth settings
        log("Initializing browser with stealth settings", "INFO")
        ua = UserAgent()
        user_agent = ua.random

        options = Options()
        options.add_argument(f"user-agent={user_agent}")
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--disable-infobars")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-gpu")
        options.add_argument("--window-size=1366,768")
        options.add_argument("--disable-web-security")
        options.add_argument("--allow-running-insecure-content")

        CHROMEDRIVER_PATH = "chromedriver.exe"
        service = Service(CHROMEDRIVER_PATH)
        driver = webdriver.Chrome(service=service, options=options)

        # Enhanced anti-detection measures
        driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
            "source": """
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en', 'ar']});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'platform', {get: () => 'Win32'});
                Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 4});
                window.chrome = {runtime: {}};
            """
        })

        # Login to the system (URL from config)
        login_url = get_sawa_setting('login.url', 'http://sp.sawaisp.sy/charge_balance_to_customer')
        log(f"Starting login process and browser initialization - URL: {login_url}", "INFO")
        driver.get(login_url)
        WebDriverWait(driver, 15).until(lambda d: d.execute_script("return document.readyState") == "complete")
        random_sleep()

        # Enter credentials from config
        username = get_sawa_setting('login.username', 'fireware')
        password = get_sawa_setting('login.password', 'idhmhgauvhx')

        log(f"Entering username: {username}", "INFO")
        driver.find_element(By.NAME, "username").send_keys(username)
        random_sleep()

        log("Entering password", "INFO")
        driver.find_element(By.NAME, "password").send_keys(password)
        random_sleep()

        log("Clicking login button", "INFO")
        driver.find_element(By.XPATH, "/html/body/section/div/div[1]/div[2]/form/input[5]").click()
        WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.ID, "select2-chosen-1")))
        log("Login successful and system ready", "SUCCESS")
        random_sleep()

        # Get order details from user input
        print("=== معالج دفع سوا - طلب واحد ===")
        print("يرجى إدخال بيانات الطلب:")

        order_id = input("رقم الطلب (Order ID): ").strip()
        phone_number = input("رقم الهاتف: ").strip()
        amount = input("المبلغ: ").strip()

        if not order_id or not phone_number or not amount:
            print("❌ يجب إدخال جميع البيانات المطلوبة")
            return False

        try:
            amount = float(amount)
        except ValueError:
            print("❌ المبلغ يجب أن يكون رقم صحيح")
            return False

        # Create order object similar to API format
        order = {
            "id": order_id,
            "phone_number": phone_number,
            "amount": amount,
            "product": 29  # Sawa product ID
        }

        print(f"\n📋 معلومات الطلب:")
        print(f"رقم الطلب: {order_id}")
        print(f"رقم الهاتف: {phone_number}")
        print(f"المبلغ: {amount}")
        print("\n🔄 بدء معالجة الطلب...")

        # Process the single order
        success = process_single_payment(driver, order)

        if success:
            print(f"\n✅ تم معالجة الطلب {order_id} بنجاح")
            log(f"Order {order_id} processed successfully", "SUCCESS")
            return True
        else:
            print(f"\n❌ فشل في معالجة الطلب {order_id}")
            log(f"Order {order_id} processing failed", "ERROR")
            return False

    except Exception as e:
        log(f"Critical error in main execution: {e}", "ERROR")
    finally:
        try:
            if 'driver' in locals() and driver is not None:
                # Try to close all windows first
                try:
                    driver.close()
                    log("Browser windows closed", "INFO")
                except:
                    pass

                # Then quit the driver
                driver.quit()
                log("Browser closed successfully", "INFO")
            else:
                log("Browser was not initialized or already closed", "INFO")
        except Exception as e:
            log(f"Error closing browser: {str(e)}", "ERROR")
            # Force kill chromedriver processes if needed
            try:
                import psutil
                for proc in psutil.process_iter(['pid', 'name']):
                    if 'chromedriver' in proc.info['name'].lower():
                        proc.kill()
                        log("Force killed chromedriver process", "WARNING")
            except:
                pass

if __name__ == "__main__":
    process_single_order_from_input()
