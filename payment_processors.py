#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Payment Processors Wrapper
==========================

This module provides unified wrapper functions for all payment processors
to be used by the API.
"""

import traceback
from datetime import datetime

# Import all payment processing functions
print("Importing payment processors...")

try:
    from payToSawa import process_single_payment_sawa, process_single_payment_sawa_api, log as log_sawa
    print("✅ Sawa imported")
except ImportError as e:
    print(f"❌ Sawa import error: {e}")
    raise

try:
    from payToLEMA import process_single_payment_Lema, process_single_payment_lema_api, log as log_lema
    print("✅ LEMA imported")
except ImportError as e:
    print(f"❌ LEMA import error: {e}")
    raise

try:
    from payToLinet import process_single_payment_linet, process_single_payment_linet_api, log as log_linet
    print("✅ Linet imported")
except ImportError as e:
    print(f"❌ Linet import error: {e}")
    raise

try:
    from payToTakamol import process_single_payment_takamol, process_single_payment_takamol_api, log as log_takamol
    print("✅ Takamol imported")
except ImportError as e:
    print(f"❌ Takamol import error: {e}")
    raise

try:
    from payToINET import process_single_payment_inet, process_single_payment_inet_api, log as log_inet
    print("✅ INET imported")
except ImportError as e:
    print(f"❌ INET import error: {e}")
    raise

try:
    from payToSyriatel import process_single_payment_syriatel, process_single_payment_syriatel_api, log as log_syriatel
    print("✅ Syriatel imported")
except ImportError as e:
    print(f"❌ Syriatel import error: {e}")
    raise

try:
    from payToMTS import process_single_payment_mts, process_single_payment_mts_api, log as log_mts
    print("✅ MTS imported")
except ImportError as e:
    print(f"❌ MTS import error: {e}")
    raise

try:
    from payToBitakat import process_single_payment_bitakat, process_single_payment_bitakat_api, log as log_bitakat
    print("✅ Bitakat imported")
except ImportError as e:
    print(f"❌ Bitakat import error: {e}")
    raise

print("All payment processors imported successfully!")

def process_sawa_payment(order):
    """
    Process Sawa payment

    Args:
        order: Order object with id, phone_number, amount, type

    Returns:
        tuple: (success, status, message)
    """
    try:
        success, status, message = process_single_payment_sawa_api(order)
        return success, status, message
    except Exception as e:
        log_sawa(f"Error in Sawa payment processing: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_lema_payment(order):
    """Process LEMA payment"""
    try:
        success, status, message = process_single_payment_lema_api(order)
        return success, status, message
    except Exception as e:
        log_lema(f"Error in LEMA payment processing: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_linet_payment(order):
    """Process Linet payment"""
    try:
        success, status, message = process_single_payment_linet_api(order)
        return success, status, message
    except Exception as e:
        log_linet(f"Error in Linet payment processing: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_takamol_payment(order):
    """Process Takamol payment"""
    try:
        success, status, message = process_single_payment_takamol_api(order)
        return success, status, message
    except Exception as e:
        log_takamol(f"Error in Takamol payment processing: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_inet_payment(order):
    """Process INET payment"""
    try:
        success, status, message = process_single_payment_inet_api(order)
        return success, status, message
    except Exception as e:
        log_inet(f"Error in INET payment processing: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_syriatel_payment(order):
    """Process Syriatel payment"""
    try:
        success, status, message = process_single_payment_syriatel_api(order)
        return success, status, message
    except Exception as e:
        log_syriatel(f"Error in Syriatel payment processing: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_mts_payment(order):
    """Process MTS payment"""
    try:
        success, status, message = process_single_payment_mts_api(order)
        return success, status, message
    except Exception as e:
        log_mts(f"Error in MTS payment processing: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

def process_bitakat_payment(order):
    """Process Bitakat payment"""
    try:
        success, status, message = process_single_payment_bitakat_api(order)
        return success, status, message
    except Exception as e:
        log_bitakat(f"Error in Bitakat payment processing: {e}", "ERROR")
        return False, 3, f"خطأ في معالجة الطلب: {str(e)}"

# Mapping of processors (using correct product IDs from payment files)
PAYMENT_PROCESSORS = {
    25: process_syriatel_payment,  # Syriatel
    29: process_sawa_payment,      # Sawa
    46: process_linet_payment,     # Linet (corrected)
    47: process_inet_payment,      # INET
    48: process_mts_payment,       # MTS
    49: process_lema_payment,      # LEMA
    50: process_bitakat_payment,   # Bitakat (original ID)
    51: process_takamol_payment    # Takamol
}

def get_payment_processor(product_id):
    """
    Get payment processor function for given product ID
    
    Args:
        product_id: Product ID
        
    Returns:
        function: Payment processor function or None
    """
    return PAYMENT_PROCESSORS.get(product_id)

def get_supported_products():
    """Get list of supported product IDs"""
    return list(PAYMENT_PROCESSORS.keys())
