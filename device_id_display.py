import subprocess
import uuid
import sys


def get_hardware_id():
    """
    Get real hardware UUID for device identification
    Returns:
        str: Hardware UUID or MAC address as fallback
    """
    try:
        command = 'powershell "Get-CimInstance -Class Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID"'
        result = subprocess.check_output(command, shell=True, stderr=subprocess.DEVNULL)
        hardware_id = result.decode().strip()
        if hardware_id and hardware_id != "FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF":
            return hardware_id
    except Exception:
        pass
    return str(uuid.getnode())


def display_device_id():
    """
    Display the device ID in a formatted way
    """
    try:
        device_id = get_hardware_id()
        
        print("=" * 50)
        print("           معرف الجهاز / Device ID")
        print("=" * 50)
        print(f"Device ID: {device_id}")
        print("=" * 50)
        
        # Additional device information
        try:
            # Get computer name
            computer_name_cmd = 'powershell "$env:COMPUTERNAME"'
            computer_name = subprocess.check_output(computer_name_cmd, shell=True, stderr=subprocess.DEVNULL)
            computer_name = computer_name.decode().strip()
            print(f"Computer Name: {computer_name}")
            
            # Get Windows version
            version_cmd = 'powershell "(Get-CimInstance Win32_OperatingSystem).Caption"'
            version = subprocess.check_output(version_cmd, shell=True, stderr=subprocess.DEVNULL)
            version = version.decode().strip()
            print(f"Operating System: {version}")
            
        except Exception:
            pass
        
        print("=" * 50)
        print("ملاحظة: احفظ معرف الجهاز هذا للحصول على التفويض")
        print("Note: Save this Device ID to get authorization")
        print("=" * 50)
        
    except Exception as e:
        print(f"خطأ في الحصول على معرف الجهاز: {e}")
        print(f"Error getting device ID: {e}")
        sys.exit(1)


if __name__ == "__main__":
    display_device_id()
    
    # Keep the window open
    input("\nاضغط Enter للخروج / Press Enter to exit...")
