#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Payment Processing API
======================

Flask API that receives payment requests and routes them to the appropriate
payment processor based on the product ID.

Request Format:
{
    "id": 586316,
    "amount": "24000",
    "number": "7530490",
    "code": null,
    "product": 44,
    "type": "2 MB",
    "date": "2025-08-07T08:22:28.000000Z"
}

Response Format:
{
    "status": 1,  # 1=success, 2=number not found, 3=payment failed, 4=subscriber has debt
    "message": "تم الدفع بنجاح",
    "request_data": {...},
    "completion_time": "2025-08-07T08:25:30.123456Z",
    "execution_time": 182.5,
    "company_name": "سوا"
}
"""

from flask import Flask, request, jsonify
from datetime import datetime, timezone
import time
import traceback
import sys
import os

# Import payment processors
try:
    from payment_processors import get_payment_processor, get_supported_products
except ImportError as e:
    print(f"Error importing payment processors: {e}")
    sys.exit(1)

app = Flask(__name__)

# Product ID to Company mapping (extracted from actual payment files)
PRODUCT_MAPPING = {
    25: {
        "name": "سيرياتيل",
        "name_en": "Syriatel"
    },
    29: {
        "name": "سوا",
        "name_en": "Sawa"
    },
    47: {
        "name": "إنت",
        "name_en": "INET"
    },
    48: {
        "name": "إم تي إس",
        "name_en": "MTS"
    },
    49: {
        "name": "ليما",
        "name_en": "LEMA"
    },
    46: {
        "name": "لاينت",
        "name_en": "Linet"
    },
    50: {
        "name": "بطاقات",
        "name_en": "Bitakat"
    },
    51: {
        "name": "تكامل",
        "name_en": "Takamol"
    }
}

# Status code descriptions
STATUS_DESCRIPTIONS = {
    1: "تم الدفع بنجاح",
    2: "الرقم غير موجود في النظام", 
    3: "فشل في عملية الدفع",
    4: "المشترك عليه دين"
}

def validate_request_data(data):
    """
    Validate incoming request data
    
    Args:
        data: Request JSON data
        
    Returns:
        tuple: (is_valid, error_message)
    """
    required_fields = ['id', 'amount', 'number', 'product', 'type']
    
    for field in required_fields:
        if field not in data:
            return False, f"Missing required field: {field}"
    
    # Validate product ID
    if data['product'] not in PRODUCT_MAPPING:
        return False, f"Unsupported product ID: {data['product']}"
    
    # Validate amount
    try:
        amount = float(data['amount'])
        if amount <= 0:
            return False, "Amount must be greater than 0"
    except (ValueError, TypeError):
        return False, "Invalid amount format"
    
    # Validate phone number
    if not str(data['number']).strip():
        return False, "Phone number cannot be empty"
    
    return True, None

def create_order_object(request_data):
    """
    Create order object for payment processors
    
    Args:
        request_data: Original request data
        
    Returns:
        dict: Order object for payment processor
    """
    return {
        "id": str(request_data['id']),
        "phone_number": str(request_data['number']),
        "amount": float(request_data['amount']),
        "type": request_data.get('type', ''),
        "product": request_data['product']
    }

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "supported_products": get_supported_products()
    })

@app.route('/process-payment', methods=['POST'])
def process_payment():
    """
    Main payment processing endpoint
    
    Receives payment request and routes to appropriate processor
    """
    start_time = time.time()
    request_timestamp = datetime.now(timezone.utc)
    
    try:
        # Get request data
        if not request.is_json:
            return jsonify({
                "status": 3,
                "message": "Request must be JSON",
                "request_data": None,
                "completion_time": datetime.now(timezone.utc).isoformat(),
                "execution_time": (time.time() - start_time) * 1000,
                "company_name": "Unknown"
            }), 400
        
        request_data = request.get_json()
        
        # Validate request
        is_valid, error_message = validate_request_data(request_data)
        if not is_valid:
            return jsonify({
                "status": 3,
                "message": f"خطأ في البيانات المرسلة: {error_message}",
                "request_data": request_data,
                "completion_time": datetime.now(timezone.utc).isoformat(),
                "execution_time": (time.time() - start_time) * 1000,
                "company_name": "Unknown"
            }), 400
        
        # Get company info and processor
        product_id = request_data['product']
        company_info = PRODUCT_MAPPING[product_id]
        company_name = company_info['name']
        processor_func = get_payment_processor(product_id)
        
        print(f"\n🔄 Processing payment request for {company_name}")
        print(f"Order ID: {request_data['id']}")
        print(f"Phone: {request_data['number']}")
        print(f"Amount: {request_data['amount']}")
        print(f"Type: {request_data['type']}")
        
        # Create order object
        order = create_order_object(request_data)
        
        # Process payment
        try:
            # Call the payment processor
            success, status, message = processor_func(order)

        except Exception as e:
            print(f"❌ Error in payment processor: {e}")
            traceback.print_exc()
            success = False
            status = 3
            message = f"خطأ في معالجة الطلب: {str(e)}"
        
        # Calculate execution time
        execution_time = (time.time() - start_time) * 1000  # in milliseconds
        completion_time = datetime.now(timezone.utc).isoformat()
        
        # Prepare response
        response = {
            "status": status,
            "message": message,
            "request_data": request_data,
            "completion_time": completion_time,
            "execution_time": round(execution_time, 2),
            "company_name": company_name
        }
        
        print(f"✅ Payment processing completed for {company_name}")
        print(f"Status: {status} - {message}")
        print(f"Execution time: {execution_time:.2f}ms")
        
        return jsonify(response)
        
    except Exception as e:
        print(f"❌ Critical error in payment API: {e}")
        traceback.print_exc()
        
        execution_time = (time.time() - start_time) * 1000
        
        return jsonify({
            "status": 3,
            "message": f"خطأ خطير في النظام: {str(e)}",
            "request_data": request.get_json() if request.is_json else None,
            "completion_time": datetime.now(timezone.utc).isoformat(),
            "execution_time": round(execution_time, 2),
            "company_name": "Unknown"
        }), 500

@app.route('/companies', methods=['GET'])
def get_companies():
    """Get list of supported companies and their product IDs"""
    companies = []
    for product_id, info in PRODUCT_MAPPING.items():
        companies.append({
            "product_id": product_id,
            "name": info['name'],
            "name_en": info['name_en']
        })
    
    return jsonify({
        "companies": companies,
        "total": len(companies)
    })

if __name__ == '__main__':
    print("🚀 Starting Payment Processing API...")
    print("📋 Supported Companies:")
    for product_id, info in PRODUCT_MAPPING.items():
        print(f"   {product_id}: {info['name']} ({info['name_en']})")
    
    print("\n🌐 API Endpoints:")
    print("   POST /process-payment - Process payment request")
    print("   GET  /health         - Health check")
    print("   GET  /companies      - List supported companies")
    
    print(f"\n🔗 Starting server on http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
