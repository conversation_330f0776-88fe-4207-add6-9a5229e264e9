const Apify = require('apify');

/**
 * Test script for TawasOwl Payment API Actor
 * Run this locally to test the actor functionality
 */

async function testHealthCheck() {
    console.log('\n🔍 Testing Health Check...');
    
    const input = {
        mode: 'health_check'
    };

    // Simulate Apify.getInput()
    global.Apify = {
        getInput: async () => input,
        setValue: async (key, value) => {
            console.log(`📤 OUTPUT [${key}]:`, JSON.stringify(value, null, 2));
        },
        utils: {
            log: {
                info: (msg, data) => console.log(`ℹ️  ${msg}`, data || ''),
                warning: (msg, data) => console.log(`⚠️  ${msg}`, data || ''),
                error: (msg, data) => console.log(`❌ ${msg}`, data || '')
            }
        },
        main: (handler) => handler()
    };

    // Import and run main
    require('./main.js');
}

async function testSinglePayment() {
    console.log('\n💳 Testing Single Payment...');
    
    const input = {
        mode: 'single_payment',
        payment_request: {
            id: 123456,
            amount: "5000",
            number: "123456789",
            product: 29, // Sawa
            type: "Test Payment",
            date: new Date().toISOString()
        },
        retry_attempts: 1,
        timeout_seconds: 60,
        debug_mode: true
    };

    global.Apify = {
        getInput: async () => input,
        setValue: async (key, value) => {
            console.log(`📤 OUTPUT [${key}]:`, JSON.stringify(value, null, 2));
        },
        utils: {
            log: {
                info: (msg, data) => console.log(`ℹ️  ${msg}`, data || ''),
                warning: (msg, data) => console.log(`⚠️  ${msg}`, data || ''),
                error: (msg, data) => console.log(`❌ ${msg}`, data || '')
            }
        },
        main: (handler) => handler()
    };

    require('./main.js');
}

async function testBatchPayment() {
    console.log('\n📦 Testing Batch Payment...');
    
    const input = {
        mode: 'batch_payment',
        batch_requests: [
            {
                id: 123456,
                amount: "5000",
                number: "123456789",
                product: 29,
                type: "Test Payment 1"
            },
            {
                id: 123457,
                amount: "3000", 
                number: "987654321",
                product: 25,
                type: "Test Payment 2"
            }
        ],
        max_concurrency: 2,
        retry_attempts: 1,
        debug_mode: true
    };

    global.Apify = {
        getInput: async () => input,
        setValue: async (key, value) => {
            console.log(`📤 OUTPUT [${key}]:`, JSON.stringify(value, null, 2));
        },
        utils: {
            log: {
                info: (msg, data) => console.log(`ℹ️  ${msg}`, data || ''),
                warning: (msg, data) => console.log(`⚠️  ${msg}`, data || ''),
                error: (msg, data) => console.log(`❌ ${msg}`, data || '')
            }
        },
        main: (handler) => handler()
    };

    require('./main.js');
}

async function runTests() {
    console.log('🧪 TawasOwl Payment API Actor - Test Suite');
    console.log('==========================================');

    try {
        // Test 1: Health Check
        await testHealthCheck();
        
        // Wait between tests
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Test 2: Single Payment (will likely fail without real credentials)
        // await testSinglePayment();
        
        // Test 3: Batch Payment (will likely fail without real credentials)  
        // await testBatchPayment();
        
        console.log('\n✅ Test suite completed!');
        console.log('\nNote: Payment tests are commented out as they require real credentials.');
        console.log('To test payments, update companies_config.json with valid credentials and uncomment the test calls above.');
        
    } catch (error) {
        console.error('\n❌ Test suite failed:', error.message);
        process.exit(1);
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}

module.exports = {
    testHealthCheck,
    testSinglePayment,
    testBatchPayment,
    runTests
};
