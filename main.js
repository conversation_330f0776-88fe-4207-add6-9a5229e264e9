const Apify = require('apify');
const { log } = Apify.utils;

// Import payment processors
const PaymentProcessors = require('./src/payment-processors');
const ConfigManager = require('./src/config-manager');

// Product ID to Company mapping
const PRODUCT_MAPPING = {
    25: { name: "سيرياتيل", name_en: "Syriatel" },
    29: { name: "سوا", name_en: "Sawa" },
    47: { name: "إن<PERSON>", name_en: "INET" },
    48: { name: "إم تي إس", name_en: "MTS" },
    49: { name: "ليما", name_en: "LEMA" },
    46: { name: "لاينت", name_en: "Linet" },
    50: { name: "بطاقات", name_en: "Bitakat" },
    51: { name: "تكامل", name_en: "Takamol" }
};

// Status code descriptions
const STATUS_DESCRIPTIONS = {
    1: "تم الدفع بنجاح",
    2: "الرقم غير موجود في النظام",
    3: "فشل في عملية الدفع",
    4: "المشترك عليه دين"
};

/**
 * Validate incoming request data
 */
function validateRequestData(data) {
    const requiredFields = ['id', 'amount', 'number', 'product', 'type'];
    
    for (const field of requiredFields) {
        if (!(field in data)) {
            return { isValid: false, error: `Missing required field: ${field}` };
        }
    }
    
    // Validate product ID
    if (!(data.product in PRODUCT_MAPPING)) {
        return { isValid: false, error: `Unsupported product ID: ${data.product}` };
    }
    
    // Validate amount
    try {
        const amount = parseFloat(data.amount);
        if (amount <= 0) {
            return { isValid: false, error: "Amount must be greater than 0" };
        }
    } catch (error) {
        return { isValid: false, error: "Invalid amount format" };
    }
    
    // Validate phone number
    if (!String(data.number).trim()) {
        return { isValid: false, error: "Phone number cannot be empty" };
    }
    
    return { isValid: true, error: null };
}

/**
 * Create order object for payment processors
 */
function createOrderObject(requestData) {
    return {
        id: String(requestData.id),
        phone_number: String(requestData.number),
        amount: parseFloat(requestData.amount),
        type: requestData.type || '',
        product: requestData.product
    };
}

/**
 * Process a single payment request
 */
async function processSinglePayment(requestData, retryAttempts = 2) {
    const startTime = Date.now();
    
    try {
        // Validate request
        const validation = validateRequestData(requestData);
        if (!validation.isValid) {
            return {
                status: 3,
                message: `خطأ في البيانات المرسلة: ${validation.error}`,
                request_data: requestData,
                completion_time: new Date().toISOString(),
                execution_time: Date.now() - startTime,
                company_name: "Unknown"
            };
        }
        
        // Get company info and processor
        const productId = requestData.product;
        const companyInfo = PRODUCT_MAPPING[productId];
        const companyName = companyInfo.name;
        
        log.info(`🔄 Processing payment request for ${companyName}`, {
            orderId: requestData.id,
            phone: requestData.number,
            amount: requestData.amount,
            type: requestData.type
        });
        
        // Create order object
        const order = createOrderObject(requestData);
        
        // Process payment with retry logic
        let lastError = null;
        for (let attempt = 0; attempt <= retryAttempts; attempt++) {
            try {
                const result = await PaymentProcessors.processPayment(productId, order);
                
                const executionTime = Date.now() - startTime;
                const response = {
                    status: result.status,
                    message: result.message,
                    request_data: requestData,
                    completion_time: new Date().toISOString(),
                    execution_time: executionTime,
                    company_name: companyName
                };
                
                log.info(`✅ Payment processing completed for ${companyName}`, {
                    status: result.status,
                    message: result.message,
                    executionTime: `${executionTime}ms`
                });
                
                return response;
                
            } catch (error) {
                lastError = error;
                log.warning(`❌ Payment attempt ${attempt + 1} failed for ${companyName}: ${error.message}`);
                
                if (attempt < retryAttempts) {
                    // Wait before retry (exponential backoff)
                    const waitTime = Math.pow(2, attempt) * 1000;
                    log.info(`⏳ Waiting ${waitTime}ms before retry...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                }
            }
        }
        
        // All attempts failed
        const executionTime = Date.now() - startTime;
        return {
            status: 3,
            message: `خطأ في معالجة الطلب: ${lastError.message}`,
            request_data: requestData,
            completion_time: new Date().toISOString(),
            execution_time: executionTime,
            company_name: companyName
        };
        
    } catch (error) {
        log.error(`❌ Critical error in payment processing: ${error.message}`, { error });
        
        const executionTime = Date.now() - startTime;
        return {
            status: 3,
            message: `خطأ خطير في النظام: ${error.message}`,
            request_data: requestData,
            completion_time: new Date().toISOString(),
            execution_time: executionTime,
            company_name: "Unknown"
        };
    }
}

/**
 * Process multiple payment requests concurrently
 */
async function processBatchPayments(requests, maxConcurrency = 3, retryAttempts = 2) {
    log.info(`🔄 Processing batch of ${requests.length} payment requests with concurrency ${maxConcurrency}`);
    
    const results = [];
    const chunks = [];
    
    // Split requests into chunks based on concurrency
    for (let i = 0; i < requests.length; i += maxConcurrency) {
        chunks.push(requests.slice(i, i + maxConcurrency));
    }
    
    // Process each chunk
    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
        const chunk = chunks[chunkIndex];
        log.info(`Processing chunk ${chunkIndex + 1}/${chunks.length} with ${chunk.length} requests`);
        
        const chunkPromises = chunk.map(request => processSinglePayment(request, retryAttempts));
        const chunkResults = await Promise.all(chunkPromises);
        
        results.push(...chunkResults);
        
        // Small delay between chunks to avoid overwhelming the system
        if (chunkIndex < chunks.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    return results;
}

/**
 * Health check function
 */
async function healthCheck() {
    const supportedProducts = PaymentProcessors.getSupportedProducts();
    
    return {
        status: "healthy",
        timestamp: new Date().toISOString(),
        supported_products: supportedProducts,
        companies: Object.entries(PRODUCT_MAPPING).map(([productId, info]) => ({
            product_id: parseInt(productId),
            name: info.name,
            name_en: info.name_en
        }))
    };
}

Apify.main(async () => {
    const input = await Apify.getInput();
    log.info('🚀 TawasOwl Payment API Actor started', { input });
    
    try {
        // Initialize configuration
        await ConfigManager.initialize();
        
        // Initialize payment processors
        await PaymentProcessors.initialize();
        
        const { mode = 'single_payment' } = input;
        
        switch (mode) {
            case 'health_check':
                const healthResult = await healthCheck();
                log.info('✅ Health check completed', healthResult);
                await Apify.setValue('OUTPUT', healthResult);
                break;
                
            case 'single_payment':
                if (!input.payment_request) {
                    throw new Error('payment_request is required for single_payment mode');
                }
                
                const singleResult = await processSinglePayment(
                    input.payment_request,
                    input.retry_attempts || 2
                );
                
                log.info('✅ Single payment completed', { 
                    status: singleResult.status,
                    company: singleResult.company_name 
                });
                await Apify.setValue('OUTPUT', singleResult);
                break;
                
            case 'batch_payment':
                if (!input.batch_requests || !Array.isArray(input.batch_requests)) {
                    throw new Error('batch_requests array is required for batch_payment mode');
                }
                
                const batchResults = await processBatchPayments(
                    input.batch_requests,
                    input.max_concurrency || 3,
                    input.retry_attempts || 2
                );
                
                const summary = {
                    total_requests: batchResults.length,
                    successful: batchResults.filter(r => r.status === 1).length,
                    failed: batchResults.filter(r => r.status !== 1).length,
                    results: batchResults
                };
                
                log.info('✅ Batch payment completed', {
                    total: summary.total_requests,
                    successful: summary.successful,
                    failed: summary.failed
                });
                await Apify.setValue('OUTPUT', summary);
                break;
                
            default:
                throw new Error(`Unknown mode: ${mode}`);
        }
        
        log.info('🏁 Actor execution completed successfully');
        
    } catch (error) {
        log.error('❌ Actor execution failed', { error: error.message });
        throw error;
    }
});
