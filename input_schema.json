{"title": "TawasOwl Payment API Input", "type": "object", "schemaVersion": 1, "properties": {"mode": {"title": "Operation Mode", "type": "string", "description": "Choose the operation mode", "default": "single_payment", "enum": ["single_payment", "batch_payment", "health_check"], "enumTitles": ["Single Payment", "Batch Payment", "Health Check"]}, "payment_request": {"title": "Payment Request", "type": "object", "description": "Single payment request data", "properties": {"id": {"title": "Order ID", "type": "integer", "description": "Unique order identifier", "example": 586316}, "amount": {"title": "Amount", "type": "string", "description": "Payment amount", "example": "24000"}, "number": {"title": "Phone Number", "type": "string", "description": "Customer phone number", "example": "7530490"}, "code": {"title": "Code", "type": ["string", "null"], "description": "Optional code", "example": null}, "product": {"title": "Product ID", "type": "integer", "description": "Product/Company ID (25=Syriatel, 29=Sawa, 46=Linet, 47=INET, 48=MTS, 49=LEMA, 50=Bitakat, 51=Takamol)", "example": 29}, "type": {"title": "Type", "type": "string", "description": "Payment type description", "example": "2 MB"}, "date": {"title": "Date", "type": "string", "description": "Request date in ISO format", "example": "2025-08-07T08:22:28.000000Z"}}, "required": ["id", "amount", "number", "product", "type"]}, "batch_requests": {"title": "Batch Payment Requests", "type": "array", "description": "Array of payment requests for batch processing", "items": {"$ref": "#/properties/payment_request"}}, "max_concurrency": {"title": "<PERSON> Concurrency", "type": "integer", "description": "Maximum number of concurrent payment processes", "default": 3, "minimum": 1, "maximum": 10}, "timeout_seconds": {"title": "Timeout (seconds)", "type": "integer", "description": "Timeout for each payment process in seconds", "default": 120, "minimum": 30, "maximum": 300}, "retry_attempts": {"title": "Retry Attempts", "type": "integer", "description": "Number of retry attempts for failed payments", "default": 2, "minimum": 0, "maximum": 5}, "debug_mode": {"title": "Debug Mode", "type": "boolean", "description": "Enable debug logging", "default": false}}, "required": ["mode"]}