const puppeteer = require('puppeteer');
const { log } = require('apify').utils;
const configManager = require('../config-manager');
const BaseProcessor = require('./base-processor');

/**
 * Sawa Payment Processor
 * Handles automated payment processing for Sawa telecom
 */
class SawaProcessor extends BaseProcessor {
    constructor() {
        super('sawa');
        this.companyName = 'سوا';
        this.productId = 29;
    }

    /**
     * Extract debt amount from Arabic balance text
     */
    extractDebtAmount(balanceText) {
        try {
            // Pattern 1: Numbers before "ل. س" (with space)
            let match = balanceText.match(/(\d+(?:\.\d+)?)\s*ل\.\s*س/);
            if (match) {
                return parseFloat(match[1]);
            }

            // Pattern 2: Numbers before "ل.س" (without space)
            match = balanceText.match(/(\d+(?:\.\d+)?)ل\.س/);
            if (match) {
                return parseFloat(match[1]);
            }

            // Pattern 3: Any number in the text
            match = balanceText.match(/(\d+(?:\.\d+)?)/);
            if (match) {
                return parseFloat(match[1]);
            }

            log.warning(`Could not extract debt amount from: ${balanceText}`);
            return -1;

        } catch (error) {
            log.error(`Error extracting debt amount: ${error.message}`);
            return -1;
        }
    }

    /**
     * Check if debt amount is acceptable based on validation rules
     */
    isDebtAcceptable(debtAmount, paymentAmount) {
        const validationRules = configManager.getValidationRules(this.companyKey);
        if (!validationRules) {
            return { acceptable: true, reason: null };
        }

        const { tolerance, allowOverpayment, zeroDebtException } = validationRules.debtRules;

        // Special case: zero debt
        if (debtAmount === 0) {
            if (zeroDebtException) {
                return { acceptable: true, reason: 'Zero debt exception allowed' };
            } else {
                return { acceptable: false, reason: 'المشترك ليس عليه دين' };
            }
        }

        // Check if debt is within tolerance
        if (debtAmount <= tolerance) {
            return { acceptable: false, reason: `الدين أقل من الحد المسموح (${tolerance})` };
        }

        // Check overpayment
        if (!allowOverpayment && paymentAmount > debtAmount) {
            return { acceptable: false, reason: 'المبلغ أكبر من الدين المطلوب' };
        }

        return { acceptable: true, reason: null };
    }

    /**
     * Process a single payment using Puppeteer
     */
    async processPayment(order) {
        let browser = null;
        let page = null;

        try {
            log.info(`🔄 Starting Sawa payment processing for order ${order.id}`);

            // Get configuration
            const credentials = configManager.getLoginCredentials(this.companyKey);
            const limits = configManager.getPaymentLimits(this.companyKey);
            const browserSettings = configManager.getBrowserSettings();

            if (!credentials) {
                throw new Error('Sawa credentials not found in configuration');
            }

            // Validate payment amount
            if (limits) {
                if (order.amount < limits.minAmount) {
                    return {
                        status: 3,
                        message: `المبلغ أقل من الحد الأدنى المسموح (${limits.minAmount})`
                    };
                }
                if (order.amount > limits.maxAmount) {
                    return {
                        status: 3,
                        message: `المبلغ أكبر من الحد الأقصى المسموح (${limits.maxAmount})`
                    };
                }
            }

            // Launch browser
            browser = await puppeteer.launch({
                headless: browserSettings.headless,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--window-size=1920,1080'
                ]
            });

            page = await browser.newPage();
            
            // Set user agent
            await page.setUserAgent(browserSettings.userAgent);
            
            // Set viewport
            await page.setViewport({ width: 1920, height: 1080 });

            // Anti-detection measures
            await page.evaluateOnNewDocument(() => {
                Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
                Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en', 'ar'] });
                Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5] });
                Object.defineProperty(navigator, 'platform', { get: () => 'Win32' });
                Object.defineProperty(navigator, 'hardwareConcurrency', { get: () => 4 });
                window.chrome = { runtime: {} };
            });

            // Navigate to login page
            log.info(`Navigating to Sawa login page: ${credentials.url}`);
            await page.goto(credentials.url, { waitUntil: 'networkidle2', timeout: 30000 });
            
            // Wait for page to load completely
            await page.waitForFunction(() => document.readyState === 'complete');
            await this.randomSleep(1000, 2000);

            // Enter credentials
            log.info(`Entering username: ${credentials.username}`);
            await page.type('input[name="username"]', credentials.username);
            await this.randomSleep(500, 1000);

            log.info('Entering password');
            await page.type('input[name="password"]', credentials.password);
            await this.randomSleep(500, 1000);

            // Click login button
            log.info('Clicking login button');
            await page.click('/html/body/section/div/div[1]/div[2]/form/input[5]');
            
            // Wait for login to complete
            await page.waitForSelector('#select2-chosen-1', { timeout: 15000 });
            log.info('✅ Login successful and system ready');
            await this.randomSleep(1000, 2000);

            // Search for customer
            log.info(`🔍 Searching for customer: ${order.phone_number}`);
            
            // Clear and enter phone number
            await page.click('#select2-chosen-1');
            await this.randomSleep(500, 1000);
            
            await page.type('#s2id_autogen1_search', order.phone_number);
            await this.randomSleep(1000, 2000);
            
            // Press Enter to search
            await page.keyboard.press('Enter');
            await this.randomSleep(2000, 3000);

            // Check if customer was found
            const customerFound = await page.evaluate(() => {
                const dropdown = document.querySelector('.select2-results');
                return dropdown && dropdown.textContent.includes('لا توجد نتائج') === false;
            });

            if (!customerFound) {
                log.warning(`Customer not found: ${order.phone_number}`);
                return {
                    status: 2,
                    message: "الرقم غير موجود في النظام"
                };
            }

            // Select the first result
            await page.click('.select2-results li:first-child');
            await this.randomSleep(1000, 2000);

            // Get customer balance/debt information
            log.info('📊 Getting customer balance information');
            const balanceInfo = await page.evaluate(() => {
                const balanceElement = document.querySelector('.balance-info, .debt-info, [class*="balance"], [class*="debt"]');
                return balanceElement ? balanceElement.textContent.trim() : '';
            });

            if (balanceInfo) {
                log.info(`Customer balance info: ${balanceInfo}`);
                
                const debtAmount = this.extractDebtAmount(balanceInfo);
                if (debtAmount > 0) {
                    const debtCheck = this.isDebtAcceptable(debtAmount, order.amount);
                    if (!debtCheck.acceptable) {
                        log.warning(`Debt validation failed: ${debtCheck.reason}`);
                        return {
                            status: 4,
                            message: debtCheck.reason
                        };
                    }
                }
            }

            // Enter payment amount
            log.info(`💰 Entering payment amount: ${order.amount}`);
            const amountInput = await page.$('input[name="amount"], #amount, .amount-input');
            if (amountInput) {
                await amountInput.click({ clickCount: 3 }); // Select all
                await amountInput.type(String(order.amount));
                await this.randomSleep(500, 1000);
            } else {
                throw new Error('Amount input field not found');
            }

            // Submit payment
            log.info('💳 Submitting payment');
            const submitButton = await page.$('input[type="submit"], button[type="submit"], .submit-btn');
            if (submitButton) {
                await submitButton.click();
                await this.randomSleep(2000, 3000);
            } else {
                throw new Error('Submit button not found');
            }

            // Wait for payment result
            await page.waitForFunction(() => {
                return document.readyState === 'complete';
            }, { timeout: 30000 });

            // Check for success/error messages
            const resultMessage = await page.evaluate(() => {
                const successSelectors = ['.success', '.alert-success', '[class*="success"]'];
                const errorSelectors = ['.error', '.alert-error', '.alert-danger', '[class*="error"]'];
                
                for (const selector of successSelectors) {
                    const element = document.querySelector(selector);
                    if (element && element.textContent.trim()) {
                        return { type: 'success', message: element.textContent.trim() };
                    }
                }
                
                for (const selector of errorSelectors) {
                    const element = document.querySelector(selector);
                    if (element && element.textContent.trim()) {
                        return { type: 'error', message: element.textContent.trim() };
                    }
                }
                
                return { type: 'unknown', message: 'No clear result message found' };
            });

            // Determine final status
            if (resultMessage.type === 'success' || 
                resultMessage.message.includes('نجح') || 
                resultMessage.message.includes('تم')) {
                
                log.info(`✅ Payment successful for order ${order.id}`);
                return {
                    status: 1,
                    message: "تم الدفع بنجاح"
                };
            } else {
                log.warning(`❌ Payment failed for order ${order.id}: ${resultMessage.message}`);
                return {
                    status: 3,
                    message: resultMessage.message || "فشل في عملية الدفع"
                };
            }

        } catch (error) {
            log.error(`❌ Sawa payment processing failed for order ${order.id}: ${error.message}`);
            return {
                status: 3,
                message: `خطأ في معالجة الطلب: ${error.message}`
            };

        } finally {
            // Clean up
            try {
                if (page) await page.close();
                if (browser) await browser.close();
                log.info('🧹 Browser cleanup completed');
            } catch (cleanupError) {
                log.warning(`Warning during cleanup: ${cleanupError.message}`);
            }
        }
    }

    /**
     * Health check for Sawa processor
     */
    async healthCheck() {
        try {
            const credentials = configManager.getLoginCredentials(this.companyKey);
            if (!credentials) {
                return { healthy: false, details: 'Configuration missing' };
            }

            // Basic connectivity test could be added here
            return { healthy: true, details: 'Configuration loaded' };

        } catch (error) {
            return { healthy: false, details: error.message };
        }
    }
}

module.exports = new SawaProcessor();
